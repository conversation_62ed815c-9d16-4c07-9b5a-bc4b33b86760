# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "64fcb0dbca4386356386c085142fa6e79c00a3326ceaa778a2d25f5d9ba61441"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.16"
  args:
    dependency: transitive
    description:
      name: args
      sha256: "4cab82a83ffef80b262ddedf47a0a8e56ee6fbf7fe21e6e768b02792034dd440"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: fd3d0dc1d451f9a252b32d95d3f0c3c487bc41a75eba2e6097cb0b9c71491b15
      url: "https://pub.dev"
    source: hosted
    version: "3.2.3"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: bb2b8403b4ccdc60ef5f25c70dead1f3d32d24b9d6117cfc087f496b178594a7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: b8eb814ebfcb4dea049680f8c1ffb2df399e4d03bf7a352c775e26fa06e02fa0
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  cloud_firestore:
    dependency: "direct main"
    description:
      name: cloud_firestore
      sha256: "65f148d9f5b4f389320abb45847120cf5e46094c1a8cbc64934ffc1e29688596"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.3"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: "43ccae09f7e0c82752e69c251c6dc5efcdff4ddcfc09564175a28657bbd74188"
      url: "https://pub.dev"
    source: hosted
    version: "5.11.3"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: e054c007217e28e07179bbae0564c2a4f6338a60bddb0c139e4834e953f4b95c
      url: "https://pub.dev"
    source: hosted
    version: "3.3.3"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.dev"
    source: hosted
    version: "1.17.0"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      sha256: d73575bb66216738db892f72ba67dc478bd3b5490fbbcf43644b57645eabc822
      url: "https://pub.dev"
    source: hosted
    version: "3.0.4"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "0b0036e8cccbfbe0555fd83c1d31a6f30b77a96b598b35a5d36dd41f718695e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+4"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: aa274aa7774f8964e4f4f38cc994db7b6158dd36e9187aaceaddc994b35c6c67
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: e35129dc44c9118cee2a5603506d823bab99c68393879edb440e0090d07586be
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "6f07cba3f7b3448d42d015bfd3d53fe12e5b36da2423f23838efc1d5fb31a263"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.8"
  device_frame:
    dependency: transitive
    description:
      name: device_frame
      sha256: afe76182aec178d171953d9b4a50a43c57c7cf3c77d8b09a48bf30c8fa04dd9d
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  device_preview:
    dependency: "direct main"
    description:
      name: device_preview
      sha256: "2f097bf31b929e15e6756dbe0ec1bcb63952ab9ed51c25dc5a2c722d2b21fdaf"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "07a5c5e8d4e6e992279e190e0352be8faa5b8f96d81c77a78b2d42f060279840"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0+3"
  drop_shadow_image:
    dependency: "direct main"
    description:
      name: drop_shadow_image
      sha256: d73a954ce7c7aaa17ea27d45c2fdbfb7f868d20edd0482bbc94a3d69308d86a7
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: a38574032c5f1dd06c4aee541789906c12ccaab8ba01446e800d9c5b79c4a978
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: fe30ac230f12f8836bb97e6e09197340d3c584526825b1746ea362a82e1e43f7
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "0df0a064ab0cad7f8836291ca6f3272edd7b83ad5b3540478ee46a0849d8022b"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "347351a8f0518f3343d79a9a0690fa67ad232fc32e2ea270677791949eac792b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "0f540f162af6f92b1de00d9c05367afdbe974625d025d8c58a8458aec7fd1dcf"
      url: "https://pub.dev"
    source: hosted
    version: "14.2.4"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: c5e79e15d1018cafffea1a6e45249db0d6bc42dbe35178634c77488179880e79
      url: "https://pub.dev"
    source: hosted
    version: "4.2.14"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: cd0cfcab7a63282049dec95a9955e7a487b5e580162310d12a82a10c0c32c546
      url: "https://pub.dev"
    source: hosted
    version: "3.2.15"
  firebase_storage:
    dependency: "direct main"
    description:
      name: firebase_storage
      sha256: fac4ebf20da0ff7c7f23596d741130a85e8bd5fac4cabf8f568f38ba55fba19d
      url: "https://pub.dev"
    source: hosted
    version: "11.0.14"
  firebase_storage_platform_interface:
    dependency: transitive
    description:
      name: firebase_storage_platform_interface
      sha256: c58dedec6d12416306e08dde463f3c65443aa2d5ccc100856ae7da75bab63e29
      url: "https://pub.dev"
    source: hosted
    version: "4.1.30"
  firebase_storage_web:
    dependency: transitive
    description:
      name: firebase_storage_web
      sha256: "28e2c6317214f63af56b1b3f2907d168534a7629ec24269b036556f399063d5a"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.23"
  firebase_ui_firestore:
    dependency: "direct main"
    description:
      name: firebase_ui_firestore
      sha256: "28db0d81a0d695c06fe5b689239eaee4642bf0bfa76c2a6337530363d21b4fc1"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.8"
  firebase_ui_localizations:
    dependency: transitive
    description:
      name: firebase_ui_localizations
      sha256: "776ba1ae30c5a493df1486cb0f880f7d69a976271d5aff490d1de7757b978201"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      sha256: "05001537bd3fac7644fa6558b09ec8c0a3f2eba78c0765f88912882b1331a5c6"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "32cd900555219333326a2d0653aaaf8671264c29befa65bbd9856d204a4c9fb3"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  flutter_inappwebview:
    dependency: "direct main"
    description:
      name: flutter_inappwebview
      sha256: f73505c792cf083d5566e1a94002311be497d984b5607f25be36d685cf6361cf
      url: "https://pub.dev"
    source: hosted
    version: "5.7.2+3"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: aeb0b80a8b3709709c9cc496cdc027c5b3216796bc0af0ce1007eaf24464fd4c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: "293995f94e120c8afce768981bd1fa9c5d6de67c547568e3b42ae2defdcbb4a0"
      url: "https://pub.dev"
    source: hosted
    version: "13.0.0"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: ccb08b93703aeedb58856e5637450bf3ffec899adb66dc325630b68994734b89
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0+1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "5ec1feac5f7f7d9266759488bc5f76416152baba9aa1b26fe572246caa00d1ab"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: c224ac897bed083dabf11f238dd11a239809b446740be0c2044608c50029ffdf
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  flutter_rating_bar:
    dependency: "direct main"
    description:
      name: flutter_rating_bar
      sha256: d2af03469eac832c591a1eba47c91ecc871fe5708e69967073c043b2d775ed93
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_share:
    dependency: "direct main"
    description:
      name: flutter_share
      sha256: ae12c1cea13b35926a109824ffac601531e40cb94ad53eeae58625eceb3eaaaa
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "1312314293acceb65b92754298754801b0e1f26a1845833b740b30415bbbcf07"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.2"
  flutter_stripe:
    dependency: "direct main"
    description:
      name: flutter_stripe
      sha256: "301b659ba120ee13452be8a89d79f62e18f6a2e5b29fb6a22c467d6c81580b39"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.0+1"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: "2f9c4d3f4836421f7067a28f8939814597b27614e021da9d63e5d3fb6e212d25"
      url: "https://pub.dev"
    source: hosted
    version: "8.2.1"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: aeac15850ef1b38ee368d4c53ba9a847e900bb2c53a4db3f6881cbb3cb684338
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: "927573f2e8a8d65c17931e21918ad0ab0666b1b636537de7c4932bdb487b190f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "6aa2946395183537c8b880962d935877325d6a09a2867c3970c05c0fed6ac482"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.5"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: f202f5d730eb8219e35e80c4461fb3a779940ad30ce8fde1586df756e3af25e6
      url: "https://pub.dev"
    source: hosted
    version: "0.8.7+3"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "1ea6870350f56af8dab716459bd9d5dc76947e29e07a2ba1d0c172eaaf4f269c"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.6+7"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "98f50d6b9f294c8ba35e25cc0d13b04bfddd25dbc8d32fa9d566a6572f2c081c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.12"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: a1546ff5861fc15812953d4733b520c3d371cec3d2859a001ff04c46c4d81883
      url: "https://pub.dev"
    source: hosted
    version: "0.8.7+3"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "1991219d9dbc42a99aff77e663af8ca51ced592cd6685c9485e3458302d3d4f8"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.3"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: "910f85bce16fb5c6f614e117efa303e85a1731bb0081edf3604a2ae6e9a3cc91"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: c33da08e136c3df0190bd5bbe51ae1df4a7d96e7954d1d7249fea2968a72d317
      url: "https://pub.dev"
    source: hosted
    version: "4.8.0"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "5e4a9cd06d447758280a8ac2405101e0e2094d2a1dbdd3756aec3fe7775ba593"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  mask_text_input_formatter:
    dependency: "direct main"
    description:
      name: mask_text_input_formatter
      sha256: "19bb7809c3c2559277e95521b3ee421e1409eb2cc85efd2feb191696c92490f4"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "107f3ed1330006a3bea63615e81cf637433f5135a52466c7caa0e7152bca9143"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  path:
    dependency: transitive
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.dev"
    source: hosted
    version: "1.8.2"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: c7edf82217d4b2952b2129a61d3ad60f1075b9299e629e149a8d2e39c2e6aad4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.14"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: da97262be945a72270513700a92b39dd2f4a54dad55d061687e2e37a6390366a
      url: "https://pub.dev"
    source: hosted
    version: "2.0.25"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: ad4c4d011830462633f03eb34445a45345673dfd4faf1ab0b4735fbd93b19183
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: "2ae08f2216225427e64ad224a24354221c2c7907e448e6e0e8b57b1eb9f10ad1"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.10"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "57585299a729335f1298b43245842678cb9f43a6310351b18fb577d6e33165ec"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: f53720498d5a543f9607db4b0e997c4b5438884de25b0f73098cc2671a51b130
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "6a2128648c854906c53fa8e33986fc0247a1116122f9534dd20e3ab9e16a32bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: cdbe7530b12ecd9eb455bdaa2fcb8d4dad22e80b8afb4798b41479d5ce26847f
      url: "https://pub.dev"
    source: hosted
    version: "6.0.5"
  responsive_builder:
    dependency: "direct main"
    description:
      name: responsive_builder
      sha256: e23ef8ce1191f878dd50d28f1496d659fc923718dbfd407b121bb167f95ba090
      url: "https://pub.dev"
    source: hosted
    version: "0.6.4"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "858aaa72d8f61637d64e776aca82e1c67e6d9ee07979123c5d17115031c1b13b"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "7fa90471a6875d26ad78c7e4a675874b2043874586891128dc5899662c97db46"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "0c1c16c56c9708aa9c361541a6f0e5cc6fc12a3232d866a687a7b7db30032b07"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9d387433ca65717bbf1be88f4d5bb18f10508917a8fa2fb02e0fd0d7479a9afa"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: fb5cf25c0235df2d0640ac1b1174f6466bd311f621574997ac59018a6664548d
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: "74083203a8eae241e0de4a0d597dbedab3b8fef5563f33cf3c12d7e93c655ca5"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "5e588e2efef56916a3b229c3bfe81e6a525665a454519ca51dbcc4236a274173"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  simple_gesture_detector:
    dependency: transitive
    description:
      name: simple_gesture_detector
      sha256: "86d08f85f1f58583b7b4b941d989f48ea6ce08c1724a1d10954a277c2ec36592"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: "500d6fec583d2c021f2d25a056d96654f910662c64f836cd2063167b8f1fa758"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.6"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "963dad8c4aa2f814ce7d2d5b1da2f36f31bd1a439d8f27e3dc189bb9d26bc684"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  stripe_android:
    dependency: transitive
    description:
      name: stripe_android
      sha256: a200ad9424ad2a27e44e6953ee1b4b02bd6e02931351184a17acc492e3a8278d
      url: "https://pub.dev"
    source: hosted
    version: "9.0.0+1"
  stripe_ios:
    dependency: transitive
    description:
      name: stripe_ios
      sha256: "6aef8a76a082aba9a08407ef2476ad42a05357370f7d2b3f8bb202bf2d612c64"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.0"
  stripe_platform_interface:
    dependency: transitive
    description:
      name: stripe_platform_interface
      sha256: "856e5f37e69fae4a394bc57ef00adb9c6fdbcb29fef2dbbc94148ad03cff0466"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "33b31b6beb98100bf9add464a36a8dd03eb10c7a8cf15aeec535e9b054aaf04b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  table_calendar:
    dependency: "direct main"
    description:
      name: table_calendar
      sha256: "7f1270313c0cdb245b583ed8518982c01d4a7e95869b3c30abcbae3b642c45d0"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.8"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.dev"
    source: hosted
    version: "0.4.16"
  timeago:
    dependency: "direct main"
    description:
      name: timeago
      sha256: "46c128312ab0ea144b146c0ac6426ddd96810efec2de3fccc425d00179cd8254"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "24c8fcdd49a805d95777a39064862133ff816ebfffe0ceff110fb5960e557964"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: "26f87ade979c47a150c9eaab93ccd2bebe70a27dc0b4b29517f2904f04eb11a5"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "75f2846facd11168d007529d6cd8fcb2b750186bea046af9711f10b907e1587e"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.10"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: a52628068d282d01a07cd86e6ba99e497aa45ce8c91159015b2416907d78e411
      url: "https://pub.dev"
    source: hosted
    version: "6.0.27"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "9af7ea73259886b92199f9e42c116072f05ff9bea2dcb339ab935dfc957392c2"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "206fb8334a700ef7754d6a9ed119e7349bc830448098f21a69bf1b4ed038cabc"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.4"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "91ee3e75ea9dadf38036200c5d3743518f4a5eb77a8d13fda1ee5764373f185e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "6c9ca697a5ae218ce56cece69d46128169a58aa8653c1b01d26fcd4aad8c4370"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "81fe91b6c4f84f222d186a9d23c73157dc4c8e1c71489c4d08be1ad3b228f1aa"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.16"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: a83ba3607a507758669cfafb03f9de09bf6e6280c14d9b9cb18f013e406dcacd
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  visibility_detector:
    dependency: "direct main"
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: a6f0236dbda0f63aa9a25ad1ff9a9d8a4eaaa5012da0dc59d21afdb1dc361ca4
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: bd512f03919aac5f1313eb8249f223bacf4927031bf60b02601f81f687689e86
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+3"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "979ee37d622dec6365e2efa4d906c37470995871fe9ae080d967e192d88286b5"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
sdks:
  dart: ">=2.19.0 <3.0.0"
  flutter: ">=3.3.0"
