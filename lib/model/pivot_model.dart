class Pivot {
  int? fromId;
  int? toId;
  String? message;
  String? updatedAt;
  String? createdAt;

  Pivot({this.fromId, this.toId, this.message, this.updatedAt, this.createdAt});

  Pivot.fromJson(Map<String, dynamic> json) {
    fromId = json['from_id'];
    toId = json['to_id'];
    message = json['message'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['from_id'] = fromId;
    data['to_id'] = toId;
    data['message'] = message;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    return data;
  }
}
