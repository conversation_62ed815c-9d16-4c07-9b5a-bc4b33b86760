
class Files {
  int? id;
  int? userId;
  String? filePath;
  String? createdAt;
  String? updatedAt;
  int? skillId;

  Files({this.id, this.userId, this.filePath, this.createdAt, this.updatedAt, this.skillId});

  Files.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    filePath = json['file_path'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    skillId = json['skill_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['file_path'] = filePath;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['skill_id'] = skillId;
    return data;
  }
}