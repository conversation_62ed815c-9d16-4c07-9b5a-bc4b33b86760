// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class ChatUserModel {
  String? chatType;
  String? senderId;
  String? senderName;
  Timestamp? timestamp;
  String? message;
  String? fileUrl;
  String? fileType;
  String? myId;
  String? otherId;
  String? groupId;
  String? imageUrl;
  String? gender;
  String? category;
  String? subCategoryId;
  String? subCategoryName;
  String? serviceId;
  String? serviceName;
  String? description;
  String? paymentStatus;
  String? amount;
  String? orderId;

  ChatUserModel({
    this.chatType,
    this.senderId,
    this.senderName,
    this.timestamp,
    this.message,
    this.fileUrl,
    this.fileType,
    this.myId,
    this.otherId,
    this.groupId,
    this.imageUrl,
    this.gender,
    this.category,
    this.subCategoryId,
    this.subCategoryName,
    this.serviceId,
    this.serviceName,
    this.description,
    this.paymentStatus,
    this.amount,
    this.orderId,
  });

  ChatUserModel.fromJson(Map<String, dynamic> json) {
    chatType = json['type'];
    senderId = json['sender_id'];
    senderName = json['sender_name'];
    timestamp = json['time_stamp'];
    message = json['message'];
    fileUrl = json['file_url'];
    fileType = json['file_type'];
    myId = json['usera_id'];
    otherId = json['userb_id'];
    groupId = json['group_id'];
    imageUrl = json['image_url'];
    gender = json['gender'];
    category = json['category'];
    subCategoryId = json['subcategory_id'];
    subCategoryName = json['subcategory_name'];
    serviceId = json['service_id'];
    serviceName = json['service_name'];
    description = json['description'];
    paymentStatus = json['payment_status'];
    amount = json['amount'];
    orderId = json['order_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = chatType;
    data['sender_id'] = senderId;
    data['sender_name'] = senderName;
    data['time_stamp'] = timestamp;
    data['message'] = message;
    data['file_url'] = fileUrl;
    data['file_type'] = fileType;
    data['usera_id'] = myId;
    data['userb_id'] = otherId;
    data['group_id'] = groupId;
    data['image_url'] = imageUrl;
    data['gender'] = gender;
    data['category'] = category;
    data['subcategory_id'] = subCategoryId;
    data['subcategory_name'] = subCategoryName;
    data['service_id'] = serviceId;
    data['service_name'] = serviceName;
    data['description'] = description;
    data['payment_status'] = paymentStatus;
    data['amount'] = amount;
    data['order_id'] = orderId;
    return data;
  }

  DateTime get timeStampDate => timestamp == null ? DateTime.now() : timestamp!.toDate();

  String get formmetedTime => DateFormat('hh:mm a').format(timeStampDate.toLocal());
}
