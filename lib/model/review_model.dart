class Review {
  int? id;
  int? userId;
  int? orderId;
  int? freelancerId;
  String? overallExperienceRating;
  String? communicationRating;
  String? qualityOfServiceRating;
  String? valueOfServiceRating;
  String? comment;
  String? createdAt;
  String? updatedAt;

  Review(
      {this.id,
      this.userId,
      this.orderId,
      this.freelancerId,
      this.overallExperienceRating,
      this.communicationRating,
      this.qualityOfServiceRating,
      this.valueOfServiceRating,
      this.comment,
      this.createdAt,
      this.updatedAt});

  Review.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    orderId = json['order_id'];
    freelancerId = json['freelancer_id'];
    overallExperienceRating = json['overall_experience_rating'];
    communicationRating = json['communication_rating'];
    qualityOfServiceRating = json['quality_of_service_rating'];
    valueOfServiceRating = json['value_of_service_rating'];
    comment = json['comment'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['order_id'] = orderId;
    data['freelancer_id'] = freelancerId;
    data['overall_experience_rating'] = overallExperienceRating;
    data['communication_rating'] = communicationRating;
    data['quality_of_service_rating'] = qualityOfServiceRating;
    data['value_of_service_rating'] = valueOfServiceRating;
    data['comment'] = comment;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }

  double? get rating {
    double total = (double.tryParse(overallExperienceRating ?? '') ?? 0.0) +
        (double.tryParse(communicationRating ?? '') ?? 0.0) +
        (double.tryParse(communicationRating ?? '') ?? 0.0) +
        (double.tryParse(valueOfServiceRating ?? '') ?? 0.0);
    if (total != 0) {
      return total / 80;
    }
    return null;
  }
}
