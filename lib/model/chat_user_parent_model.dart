// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class ChatUserParentModel {
  String? type;
  Timestamp? timestamp;
  String? message;
  int? userAbadge;
  int? userBbadge;

  ChatUserParentModel({
    this.type,
    this.userAbadge,
    this.timestamp,
    this.message,
  });

  ChatUserParentModel.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    timestamp = json['time_stamp'];
    message = json['message'];
    userAbadge = json['usera_badge'];
    userBbadge = json['userb_badge'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['time_stamp'] = timestamp;
    data['message'] = message;
    data['usera_badge'] = userAbadge;
    data['userb_badge'] = userBbadge;

    return data;
  }

  DateTime get timeStampDate => timestamp == null ? DateTime.now() : timestamp!.toDate();

  String get formmetedTime => DateFormat('hh:mm a').format(timeStampDate.toLocal());

  int get userAbadgeValue => userAbadge ?? 0;
  int get userBbadgeValue => userBbadge ?? 0;

  @override
  String toString() {
    return 'ChatUserParentModel(type: $type, timestamp: $timestamp, message: $message, userAbadge: $userAbadge, userBbadge: $userBbadge)';
  }
}
