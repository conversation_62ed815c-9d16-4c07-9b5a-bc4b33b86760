import 'package:trendi_people/model/user_model.dart';

import 'category_model.dart';
import 'freeliancer_model.dart';

class OrderHistoryModel {
  int? id;
  int? userId;
  int? freelancerId;
  String? name;
  String? email;
  String? mobileNumber;
  String? address;
  String? startDate;
  String? endDate;
  String? gender;
  String? type;
  String? orderPrice;
  String? serviceFee;
  String? totalPrice;
  String? createdAt;
  String? updatedAt;
  String? availability;
  String? paymentMethodId;
  String? status;
  String? chargeId;
  int? isConfirmed;
  String? paymentId;
  String? notes;
  String? deliveryOption;
  num? deliveryCharge;
  String? description;
  CategoryModel? mainCategory;
  FreeliancerModel? freelancer;
  List<CategoryModel>? categories;
  UserModel? user;

  OrderHistoryModel(
      {this.id,
      this.userId,
      this.freelancerId,
      this.name,
      this.email,
      this.mobileNumber,
      this.address,
      this.startDate,
      this.endDate,
      this.gender,
      this.type,
      this.orderPrice,
      this.serviceFee,
      this.totalPrice,
      this.createdAt,
      this.updatedAt,
      this.availability,
      this.paymentMethodId,
      this.status,
      this.chargeId,
      this.isConfirmed,
      this.paymentId,
      this.notes,
      this.deliveryOption,
      this.deliveryCharge,
      this.description,
      this.mainCategory,
      this.freelancer,
      this.categories,
      this.user});

  OrderHistoryModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    freelancerId = json['freelancer_id'];
    name = json['name'];
    email = json['email'];
    mobileNumber = json['mobile_number'];
    address = json['address'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    gender = json['gender'];
    type = json['type'];
    orderPrice = json['order_price'];
    serviceFee = json['service_fee'];
    totalPrice = json['total_price'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    availability = json['availability'];
    paymentMethodId = json['payment_method_id'];
    status = json['status'];
    chargeId = json['charge_id'];
    isConfirmed = json['is_confirmed'];
    paymentId = json['payment_id'];
    notes = json['notes'];
    deliveryOption = json['delivery_option'];
    deliveryCharge = json['delivery_charge'];
    description = json['description'];
    mainCategory = json['main_category'] != null ? CategoryModel.fromJson(json['main_category']) : null;
    freelancer = json['freelancer'] != null ? FreeliancerModel.fromJson(json['freelancer']) : null;
    if (json['categories'] != null) {
      categories = <CategoryModel>[];
      json['categories'].forEach((v) {
        categories!.add(CategoryModel.fromJson(v));
      });
    }
    user = json['user'] != null ? UserModel.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['freelancer_id'] = freelancerId;
    data['name'] = name;
    data['email'] = email;
    data['mobile_number'] = mobileNumber;
    data['address'] = address;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['gender'] = gender;
    data['type'] = type;
    data['order_price'] = orderPrice;
    data['service_fee'] = serviceFee;
    data['total_price'] = totalPrice;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['availability'] = availability;
    data['payment_method_id'] = paymentMethodId;
    data['status'] = status;
    data['charge_id'] = chargeId;
    data['is_confirmed'] = isConfirmed;
    data['payment_id'] = paymentId;
    data['notes'] = notes;
    data['delivery_option'] = deliveryOption;
    data['delivery_charge'] = deliveryCharge;
    data['description'] = description;
    if (mainCategory != null) {
      data['main_category'] = mainCategory!.toJson();
    }
    if (freelancer != null) {
      data['freelancer'] = freelancer!.toJson();
    }
    if (categories != null) {
      data['categories'] = categories!.map((v) => v.toJson()).toList();
    }
    if (user != null) {
      data['user'] = user!.toJson();
    }
    return data;
  }
}
