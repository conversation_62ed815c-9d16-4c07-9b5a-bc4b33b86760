
class PricingModel {
  int? id;
  int? userId;
  int? categoryId;
  String? createdAt;
  String? updatedAt;
  int? price;
  int? subCategoryId;

  PricingModel({this.id, this.userId, this.categoryId, this.createdAt, this.updatedAt, this.price, this.subCategoryId});

  PricingModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    categoryId = json['category_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    price = json['price'];
    subCategoryId = json['sub_category_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['category_id'] = categoryId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['price'] = price;
    data['sub_category_id'] = subCategoryId;
    return data;
  }
}
