
import 'files_model.dart';

class Skills {
  int? id;
  int? userId;
  String? skillName;
  String? createdAt;
  String? updatedAt;
  List<Files>? files;

  Skills({this.id, this.userId, this.skillName, this.createdAt, this.updatedAt, this.files});

  Skills.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    skillName = json['skill_name'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    if (json['files'] != null) {
      files = <Files>[];
      json['files'].forEach((v) {
        files!.add(Files.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['skill_name'] = skillName;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (files != null) {
      data['files'] = files!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}