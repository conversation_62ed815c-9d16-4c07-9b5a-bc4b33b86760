class ActiveLeaves {
  int? id;
  int? userId;
  String? fromDate;
  String? toDate;
  String? createdAt;
  String? updatedAt;

  ActiveLeaves({this.id, this.userId, this.fromDate, this.toDate, this.createdAt, this.updatedAt});

  ActiveLeaves.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    fromDate = json['from_date'];
    toDate = json['to_date'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['from_date'] = fromDate;
    data['to_date'] = toDate;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }

  DateTime? get startDate {
    return DateTime.tryParse(fromDate ?? '');
  }

  DateTime? get endDate {
    return DateTime.tryParse(toDate ?? '');
  }
}
