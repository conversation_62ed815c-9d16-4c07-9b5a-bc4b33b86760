class CategoryModel {
  int? id;
  int? parentId;
  String? name;
  String? type;
  String? image;
  String? price;
  String? createdAt;
  String? updatedAt;
  String? femaleImage;
  String? otherImage;
  int? isForMale;
  int? isForFemale;
  int? isForOther;
  int? subCategoriesCount;

  CategoryModel(
      {this.id,
      this.parentId,
      this.name,
      this.type,
      this.image,
      this.price,
      this.createdAt,
      this.updatedAt,
      this.femaleImage,
      this.otherImage,
      this.isForMale,
      this.isForFemale,
      this.isForOther,
      this.subCategoriesCount});

  CategoryModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    parentId = json['parent_id'];
    name = json['name'];
    type = json['type'];
    image = json['image'];
    price = json['price'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    femaleImage = json['female_image'];
    otherImage = json['other_image'];
    isForMale = json['is_for_male'];
    isForFemale = json['is_for_female'];
    isForOther = json['is_for_other'];
    subCategoriesCount = json['sub_categories_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['parent_id'] = parentId;
    data['name'] = name;
    data['type'] = type;
    data['image'] = image;
    data['price'] = price;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['female_image'] = femaleImage;
    data['other_image'] = otherImage;
    data['is_for_male'] = isForMale;
    data['is_for_female'] = isForFemale;
    data['is_for_other'] = isForOther;
    data['sub_categories_count'] = subCategoriesCount;
    return data;
  }
}
