class ReportModel {
  int? userId;
  int? reporterId;
  int? userFileId;
  String? description;
  String? updatedAt;
  String? createdAt;
  int? id;

  ReportModel(
      {this.userId, this.reporterId, this.userFileId, this.description, this.updatedAt, this.createdAt, this.id});

  ReportModel.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    reporterId = json['reporter_id'];
    userFileId = json['user_file_id'];
    description = json['description'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['reporter_id'] = reporterId;
    data['user_file_id'] = userFileId;
    data['description'] = description;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['id'] = id;
    return data;
  }
}
