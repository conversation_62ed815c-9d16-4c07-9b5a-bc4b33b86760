import 'package:trendi_people/model/pivot_model.dart';

class UserModel {
  int? id;
  String? firstName;
  String? lastName;
  String? bio;
  String? email;
  String? phoneNumber;
  String? role;
  int? isEmailVerified;
  String? profileImage;
  String? latitude;
  String? longitude;
  int? isActive;
  String? createdAt;
  String? updatedAt;
  String? stripeId;
  String? pmType;
  String? pmLastFour;
  String? trialEndsAt;
  String? connectId;
  String? address;
  int? isHide;
  String? coverImage;
  Pivot? pivot;
  String? averageRating;

  UserModel(
      {this.id,
      this.firstName,
      this.lastName,
      this.bio,
      this.email,
      this.phoneNumber,
      this.role,
      this.isEmailVerified,
      this.profileImage,
      this.latitude,
      this.longitude,
      this.isActive,
      this.createdAt,
      this.updatedAt,
      this.stripeId,
      this.pmType,
      this.pmLastFour,
      this.trialEndsAt,
      this.connectId,
      this.address,
      this.isHide,
      this.coverImage,
      this.pivot,
      this.averageRating});

  UserModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    bio = json['bio'];
    email = json['email'];
    phoneNumber = json['phone_number'];
    role = json['role'];
    isEmailVerified = json['is_email_verified'];
    profileImage = json['profile_image'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    isActive = json['is_active'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    stripeId = json['stripe_id'];
    pmType = json['pm_type'];
    pmLastFour = json['pm_last_four'];
    trialEndsAt = json['trial_ends_at'];
    connectId = json['connect_id'];
    address = json['address'];
    isHide = json['is_hide'];
    coverImage = json['cover_image'];
    pivot = json['pivot'] != null ? Pivot.fromJson(json['pivot']) : null;
    averageRating = json['average_rating'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['bio'] = bio;
    data['email'] = email;
    data['phone_number'] = phoneNumber;
    data['role'] = role;
    data['is_email_verified'] = isEmailVerified;
    data['profile_image'] = profileImage;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['is_active'] = isActive;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['stripe_id'] = stripeId;
    data['pm_type'] = pmType;
    data['pm_last_four'] = pmLastFour;
    data['trial_ends_at'] = trialEndsAt;
    data['connect_id'] = connectId;
    data['address'] = address;
    data['is_hide'] = isHide;
    data['cover_image'] = coverImage;
    if (pivot != null) {
      data['pivot'] = pivot!.toJson();
    }
    data['average_rating'] = averageRating;
    return data;
  }
}
