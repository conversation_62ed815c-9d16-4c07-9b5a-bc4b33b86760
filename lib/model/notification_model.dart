
class NotificationModel {
  int? id;
  int? userId;
  int? orderId;
  String? title;
  String? message;
  String? onClickAction;
  String? type;
  String? createdAt;
  String? updatedAt;
  Order? order;

  NotificationModel(
      {this.id,
      this.userId,
      this.orderId,
      this.title,
      this.message,
      this.onClickAction,
      this.type,
      this.createdAt,
      this.updatedAt,
      this.order});

  NotificationModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    orderId = json['order_id'];
    title = json['title'];
    message = json['message'];
    onClickAction = json['on_click_action'];
    type = json['type'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    order = json['order'] != null ? Order.fromJson(json['order']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['order_id'] = orderId;
    data['title'] = title;
    data['message'] = message;
    data['on_click_action'] = onClickAction;
    data['type'] = type;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (order != null) {
      data['order'] = order!.toJson();
    }
    return data;
  }
}

class Order {
  int? id;
  int? userId;
  int? freelancerId;
  String? name;
  String? email;
  String? mobileNumber;
  String? address;
  String? startDate;
  String? endDate;
  String? gender;
  String? type;
  String? orderPrice;
  String? serviceFee;
  String? totalPrice;
  String? createdAt;
  String? updatedAt;
  String? availability;
  String? paymentMethodId;
  String? status;
  String? chargeId;
  int? isConfirmed;
  String? paymentId;
  String? notes;
  String? deliveryOption;
  num? deliveryCharge;
  String? description;
  MainCategory? mainCategory;

  Order(
      {this.id,
      this.userId,
      this.freelancerId,
      this.name,
      this.email,
      this.mobileNumber,
      this.address,
      this.startDate,
      this.endDate,
      this.gender,
      this.type,
      this.orderPrice,
      this.serviceFee,
      this.totalPrice,
      this.createdAt,
      this.updatedAt,
      this.availability,
      this.paymentMethodId,
      this.status,
      this.chargeId,
      this.isConfirmed,
      this.paymentId,
      this.notes,
      this.deliveryOption,
      this.deliveryCharge,
      this.description,
      this.mainCategory});

  Order.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    freelancerId = json['freelancer_id'];
    name = json['name'];
    email = json['email'];
    mobileNumber = json['mobile_number'];
    address = json['address'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    gender = json['gender'];
    type = json['type'];
    orderPrice = json['order_price'];
    serviceFee = json['service_fee'];
    totalPrice = json['total_price'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    availability = json['availability'];
    paymentMethodId = json['payment_method_id'];
    status = json['status'];
    chargeId = json['charge_id'];
    isConfirmed = json['is_confirmed'];
    paymentId = json['payment_id'];
    notes = json['notes'];
    deliveryOption = json['delivery_option'];
    deliveryCharge = json['delivery_charge'];
    description = json['description'];
    mainCategory = json['main_category'] != null
        ? MainCategory.fromJson(json['main_category'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['freelancer_id'] = freelancerId;
    data['name'] = name;
    data['email'] = email;
    data['mobile_number'] = mobileNumber;
    data['address'] = address;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['gender'] = gender;
    data['type'] = type;
    data['order_price'] = orderPrice;
    data['service_fee'] = serviceFee;
    data['total_price'] = totalPrice;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['availability'] = availability;
    data['payment_method_id'] = paymentMethodId;
    data['status'] = status;
    data['charge_id'] = chargeId;
    data['is_confirmed'] = isConfirmed;
    data['payment_id'] = paymentId;
    data['notes'] = notes;
    data['delivery_option'] = deliveryOption;
    data['delivery_charge'] = deliveryCharge;
    data['description'] = description;
    if (mainCategory != null) {
      data['main_category'] = mainCategory!.toJson();
    }
    return data;
  }
}

class MainCategory {
  int? id;
  int? parentId;
  String? name;
  String? type;
  String? image;
  String? price;
  String? createdAt;
  String? updatedAt;
  String? femaleImage;
  String? otherImage;
  int? isForMale;
  int? isForFemale;
  int? isForOther;

  MainCategory(
      {this.id,
      this.parentId,
      this.name,
      this.type,
      this.image,
      this.price,
      this.createdAt,
      this.updatedAt,
      this.femaleImage,
      this.otherImage,
      this.isForMale,
      this.isForFemale,
      this.isForOther});

  MainCategory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    parentId = json['parent_id'];
    name = json['name'];
    type = json['type'];
    image = json['image'];
    price = json['price'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    femaleImage = json['female_image'];
    otherImage = json['other_image'];
    isForMale = json['is_for_male'];
    isForFemale = json['is_for_female'];
    isForOther = json['is_for_other'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['parent_id'] = parentId;
    data['name'] = name;
    data['type'] = type;
    data['image'] = image;
    data['price'] = price;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['female_image'] = femaleImage;
    data['other_image'] = otherImage;
    data['is_for_male'] = isForMale;
    data['is_for_female'] = isForFemale;
    data['is_for_other'] = isForOther;
    return data;
  }
}
