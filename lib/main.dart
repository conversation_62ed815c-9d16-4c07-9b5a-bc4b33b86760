import 'package:device_preview/device_preview.dart';
import 'package:flutter/material.dart';
import 'package:trendi_people/screen/splash_screen.dart';
import 'package:trendi_people/utility/app_theme.dart';
import 'package:trendi_people/utility/firebase_messaging_services.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await FirebaseMessagingService.initializeMain();
  runApp(
    DevicePreview(
      enabled: false,
      tools: const [...DevicePreview.defaultTools],
      builder: (context) => const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      builder: DevicePreview.appBuilder,
      debugShowCheckedModeBanner: false,
      title: 'Trendi-People-Shopper',
      theme: themeData,
      home: const SplashScreen(),
    );
  }
}
