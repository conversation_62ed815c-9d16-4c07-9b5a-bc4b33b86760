import 'package:trendi_people/model/report_model.dart';

class ReportResponse {
  ReportModel? data;
  String? message;
  String? status;

  ReportResponse({this.data, this.message, this.status});

  ReportResponse.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? ReportModel.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
