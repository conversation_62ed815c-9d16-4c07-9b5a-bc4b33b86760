import '../model/order_detail_model.dart';

class OrderDetailResponce {
  OrderDetailModel? data;
  String? message;
  String? status;

  OrderDetailResponce({this.data, this.message, this.status});

  OrderDetailResponce.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? OrderDetailModel.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
