import '../model/pricing_model.dart';

class PricingResponce {
  List<PricingModel>? data;
  String? totalCost;
  String? serviceCharges;
  String? collectionCharges;
  String? deliveryCharges;
  String? totalPayableCost;
  String? message;
  String? status;

  PricingResponce(
      {this.data,
      this.totalCost,
      this.serviceCharges,
      this.collectionCharges,
      this.deliveryCharges,
      this.totalPayableCost,
      this.message,
      this.status});

  PricingResponce.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <PricingModel>[];
      json['data'].forEach((v) {
        data!.add(PricingModel.fromJson(v));
      });
    }
    totalCost = json['total_cost'];
    serviceCharges = json['service_charges'];
    collectionCharges = json['collection_charges'];
    deliveryCharges = json['delivery_charges'];
    totalPayableCost = json['total_payable_cost'];
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['total_cost'] = totalCost;
    data['service_charges'] = serviceCharges;
    data['collection_charges'] = collectionCharges;
    data['delivery_charges'] = deliveryCharges;
    data['total_payable_cost'] = totalPayableCost;
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
