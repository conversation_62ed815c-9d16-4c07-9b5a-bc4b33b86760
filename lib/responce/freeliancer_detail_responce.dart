import '../model/freelancer_detail_model.dart';

class FreelancerDetailResponce {
  String? avgRating;
  FreelancerDetailModel? data;
  String? message;
  String? status;

  FreelancerDetailResponce({this.avgRating, this.data, this.message, this.status});

  FreelancerDetailResponce.fromJson(Map<String, dynamic> json) {
    avgRating = json['avg_rating'];
    data = json['data'] != null ? FreelancerDetailModel.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['avg_rating'] = avgRating;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
