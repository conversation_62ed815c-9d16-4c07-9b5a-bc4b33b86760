import '../model/notification_model.dart';

class NotificationReponce {
  List<NotificationModel>? data;
  String? message;
  String? status;

  NotificationReponce({this.data, this.message, this.status});

  NotificationReponce.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <NotificationModel>[];
      json['data'].forEach((v) {
        data!.add(NotificationModel.fromJson(v));
      });
    }
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
