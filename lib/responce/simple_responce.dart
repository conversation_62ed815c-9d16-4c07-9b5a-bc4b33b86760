class SimpleResponse {
  String? message;
  String? status;
  String? data;

  SimpleResponse({this.message, this.status});

  SimpleResponse.fromJson(Map<String, dynamic> json) {
    try {
      data = json['data'];
    } catch (e) {}
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['data'] = data;

    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
