import 'package:trendi_people/model/user_model.dart';

class ChatUserListResponse {
  String? message;
  String? status;
  int? currentPage;
  List<UserModel>? data;
  String? firstPageUrl;
  int? from;

  String? path;
  int? perPage;

  int? to;

  ChatUserListResponse(
      {this.message,
      this.status,
      this.currentPage,
      this.data,
      this.firstPageUrl,
      this.from,
      this.path,
      this.perPage,
      this.to});

  ChatUserListResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    status = json['status'];
    currentPage = json['current_page'];
    if (json['data'] != null) {
      data = <UserModel>[];
      json['data'].forEach((v) {
        data!.add(UserModel.fromJson(v));
      });
    }
    firstPageUrl = json['first_page_url'];
    from = json['from'];

    path = json['path'];
    perPage = json['per_page'];

    to = json['to'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['status'] = status;
    data['current_page'] = currentPage;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['first_page_url'] = firstPageUrl;
    data['from'] = from;

    data['path'] = path;
    data['per_page'] = perPage;

    data['to'] = to;
    return data;
  }
}
