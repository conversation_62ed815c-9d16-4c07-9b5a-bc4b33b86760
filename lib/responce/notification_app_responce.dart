class NotificationAppResponce {
  UserData? userData;
  String? title;
  String? body;
  String? clickAction;

  NotificationAppResponce({this.userData, this.title, this.body, this.clickAction});

  NotificationAppResponce.fromJson(Map<String, dynamic> json) {
    userData = json['user_data'] != null ? UserData.fromJson(json['user_data']) : null;
    title = json['title'];
    body = json['body'];
    clickAction = json['click_action'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (userData != null) {
      data['user_data'] = userData!.toJson();
    }
    data['title'] = title;
    data['body'] = body;
    data['click_action'] = clickAction;
    return data;
  }
}

class UserData {
  String? name;
  int? id;

  UserData({this.name, this.id});

  UserData.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['id'] = id;
    return data;
  }
}
