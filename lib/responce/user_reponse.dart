import '../model/user_model.dart';

class UserResponce {
  UserModel? data;
  String? token;
  String? message;
  String? status;

  UserResponce({this.data, this.token, this.message, this.status});

  UserResponce.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? UserModel.fromJson(json['data']) : null;
    token = json['token'];
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['token'] = token;
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
