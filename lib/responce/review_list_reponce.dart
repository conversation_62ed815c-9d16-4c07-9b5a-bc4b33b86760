import 'package:trendi_people/model/user_model.dart';

import '../model/review_list_model.dart';

class ReviewsListReponce {
  String? message;
  String? status;
  int? currentPage;
  List<ReviewListModel>? data;
  String? firstPageUrl;
  int? from;
  String? nextPageUrl;
  String? path;
  String? perPage;
  String? prevPageUrl;
  int? to;

  ReviewsListReponce(
      {this.message,
      this.status,
      this.currentPage,
      this.data,
      this.firstPageUrl,
      this.from,
      this.nextPageUrl,
      this.path,
      this.perPage,
      this.prevPageUrl,
      this.to});

  ReviewsListReponce.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    status = json['status'];
    currentPage = json['current_page'];
    if (json['data'] != null) {
      data = <ReviewListModel>[];
      json['data'].forEach((v) {
        data!.add(ReviewListModel.fromJson(v));
      });
    }
    firstPageUrl = json['first_page_url'];
    from = json['from'];
    nextPageUrl = json['next_page_url'];
    path = json['path'];
    perPage = json['per_page'];
    prevPageUrl = json['prev_page_url'];
    to = json['to'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['status'] = status;
    data['current_page'] = currentPage;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['first_page_url'] = firstPageUrl;
    data['from'] = from;
    data['next_page_url'] = nextPageUrl;
    data['path'] = path;
    data['per_page'] = perPage;
    data['prev_page_url'] = prevPageUrl;
    data['to'] = to;
    return data;
  }
}
