import '../model/order_history_model.dart';

class OrderHistoryResponce {
  List<OrderHistoryModel>? data;
  String? message;
  String? status;

  OrderHistoryResponce({this.data, this.message, this.status});

  OrderHistoryResponce.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <OrderHistoryModel>[];
      json['data'].forEach((v) {
        data!.add(OrderHistoryModel.fromJson(v));
      });
    }
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
