import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../utility/app_colors.dart';

class AboutUsScreen extends StatefulWidget {
  const AboutUsScreen({super.key});

  @override
  State<AboutUsScreen> createState() => _AboutUsScreenState();
}

class _AboutUsScreenState extends State<AboutUsScreen> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          appBar: AppBar(
            backgroundColor: AppColors.primaryColor,
            centerTitle: true,
            title: const Text('About Us', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          ),
          body: InAppWebView(
              onLoadStart: (controller, url) async {
                setState(() {});
              },
              initialUrlRequest: URLRequest(
                url: Uri.parse('https://trendipeople.com/about/'),
              ))),
    );
  }
}
