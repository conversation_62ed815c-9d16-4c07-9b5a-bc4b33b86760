import 'package:flutter/material.dart';
import 'package:trendi_people/responce/comman_response.dart';
import 'package:trendi_people/screen/sign_in_screen.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_widget.dart';

import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_field_widget.dart';

class ForgotPassWordScreen extends StatefulWidget {
  const ForgotPassWordScreen({super.key});

  @override
  State<ForgotPassWordScreen> createState() => _ForgotPassWordScreenState();
}

class _ForgotPassWordScreenState extends State<ForgotPassWordScreen> {
  final emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Form(
          key: _formKey,
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 25,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      100.verticalSpace,
                      const TextWidget(
                        text: 'Have you forgotten your password?',
                        fontsize: 25,
                        textcolor: AppColors.whiteColor,
                        maxLines: 3,
                      ),
                      20.verticalSpace,
                      const TextWidget(
                        text:
                            'Don’t panic, please use the email address that you used when you originally created your account and we’ll send you instructions on how to setup a new password.',
                        fontsize: 15,
                        fontweight: FontWeight.normal,
                        textcolor: AppColors.whiteColor,
                        maxLines: 5,
                      ),
                      20.verticalSpace,
                      AppTextField(
                        inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                        controller: emailController,
                        hintText: 'Email',
                        preffixIcon: Padding(
                          padding: const EdgeInsets.only(left: 25, right: 20),
                          child: Image.asset(
                            AppAssets.mailIcon,
                            color: AppColors.whiteColor,
                            height: 24,
                            width: 24,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter your email';
                          } else {
                            if (!RegExp(
                                    r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
                                .hasMatch(value)) {
                              return 'Enter valid email';
                            }
                          }

                          return null;
                        },
                      ),
                      20.verticalSpace,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const TextWidget(
                            text: 'Suddenly remembered your password?',
                            textcolor: AppColors.whiteColor,
                            fontsize: 14,
                            letterSpacing: 0.5,
                            fontweight: FontWeight.w400,
                          ),
                          3.horizontalSpace,
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const SignInScreen(),
                                  ));
                            },
                            child: const TextWidget(
                              text: ' Log In',
                              textcolor: AppColors.primaryColor,
                              fontsize: 14,
                              fontweight: FontWeight.w400,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                      20.verticalSpace,
                      CommonButtonWidget(
                          onTap: () {
                            if (_formKey.currentState!.validate()) {
                              forgutPassWord();
                            }
                          },
                          text: 'Send'),
                    ],
                  ),
                ),
              ),
              ValueListenableBuilder(
                valueListenable: isLoading,
                builder: ((context, value, _) {
                  if (isLoading.value) {
                    return Container(
                        height: MediaQuery.of(context).size.height,
                        color: AppColors.whiteColor,
                        child: Utility.progress());
                  }
                  return const SizedBox();
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void forgutPassWord() async {
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;
      var request = <String, dynamic>{};
      request['email'] = emailController.text;
      request['role'] = 'user';

      // request["firebase_id"] = FirebaseMessagingService.token;
      CommonResponse userResponse =
          // ignore: use_build_context_synchronously
          CommonResponse.fromJson(await ApiManager().postCall(AppString.resetpasswordUrl, request, context));
      if (userResponse.status == "1") {
        // ignore: use_build_context_synchronously
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => const SignInScreen(),
          ),
          (Route<dynamic> route) => false,
        );
      }
      Utility.toast(message: userResponse.message);

      isLoading.value = false;
    } else {
      Utility.toast(message: 'No Internet');
    }
  }
}
