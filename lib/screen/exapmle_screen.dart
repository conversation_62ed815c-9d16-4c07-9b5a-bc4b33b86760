import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:trendi_people/screen/size_select_scren.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../utility/app_colors.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_widget.dart';

class ExapmleScreen extends StatefulWidget {
  const ExapmleScreen({super.key});

  @override
  State<ExapmleScreen> createState() => _ExapmleScreenState();
}

class _ExapmleScreenState extends State<ExapmleScreen> {
  List<Widget> image = <Widget>[
    Image.asset('assets/image/Rectangle 1.png', fit: BoxFit.cover),
    Image.asset('assets/image/Rectangle 2.png', fit: BoxFit.cover),
    Image.asset('assets/image/Rectangle 5.png', fit: BoxFit.cover),
    Image.asset('assets/image/Rectangle 1.png', fit: BoxFit.cover),
    Image.asset('assets/image/Rectangle 3.png', fit: BoxFit.cover),
    DottedBorder(
      color: AppColors.primaryColor,
      strokeWidth: 4,
      borderType: BorderType.RRect,
      radius: const Radius.circular(20),
      child: Container(
        height: 150,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(10),
            height: 50,
            width: 50,
            decoration: BoxDecoration(border: Border.all(color: AppColors.grey4), shape: BoxShape.circle),
            child: Image.asset('assets/image/add-circle.png', fit: BoxFit.cover),
          ),
        ),
      ),
    )
  ];
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(190.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    Container(
                      height: 45,
                      width: 45,
                      decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          image: DecorationImage(
                              image: AssetImage(
                                "assets/image/tiger.jpg",
                              ),
                              scale: 2)),
                    ),
                  ],
                ),
                30.verticalSpace,
                const TextWidget(
                  text: 'Example',
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Photos of example',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 20),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TextWidget(
                  text: 'Add some kind of style you want.',
                  fontsize: 22,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                20.verticalSpace,
                StaggeredGrid.count(
                  crossAxisCount: 2,
                  mainAxisSpacing: 4,
                  crossAxisSpacing: 4,
                  children: image,
                ),
                30.verticalSpace,
                CommonButtonWidget(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SizeSelectScreen(),
                      ),
                    );
                  },
                  text: 'Done',
                ),
                const SizedBox(
                  height: 15,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
