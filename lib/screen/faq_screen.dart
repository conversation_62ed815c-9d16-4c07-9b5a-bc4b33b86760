import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';


import '../utility/app_colors.dart';

class FAQScreen extends StatefulWidget {
  const FAQScreen({super.key});

  @override
  State<FAQScreen> createState() => _FAQScreenState();
}

class _FAQScreenState extends State<FAQScreen> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          appBar: AppBar(
            backgroundColor: AppColors.primaryColor,
            centerTitle: true,
            title: const Text('FAQ', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          ),
          body: InAppWebView(
              onLoadStart: (controller, url) async {
                setState(() {});
              },
              initialUrlRequest: URLRequest(
                url: Uri.parse('https://trendipeople.com/faq/'),
              ))),
    );
  }
}
