import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_widget.dart';

import '../model/user_model.dart';
import '../responce/user_reponse.dart';
import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_field_widget.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({Key? key}) : super(key: key);

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  final currentpasswordController = TextEditingController();
  final newpasswordController = TextEditingController();
  final confirmpasswordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  UserModel? user;
  @override
  void initState() {
    super.initState();

    fetchCustomerData();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(190.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 45,
                      width: 45,
                      child: Utility.imageLoader(
                        url: user?.profileImage ?? '',
                        isShapeCircular: true,
                        shape: BoxShape.circle,
                        placeholder: AppAssets.placeHolderImage,
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                const TextWidget(
                  text: "Change Password",
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 30,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 12.0),
                      child: Row(
                        children: [
                          const TextWidget(
                            text: 'Current Password',
                            textcolor: AppColors.blackColor,
                            fontsize: 18,
                            fontweight: FontWeight.w400,
                          ),
                          TextWidget(
                            text: ' *',
                            textcolor: AppColors.red,
                            fontsize: 18,
                            fontweight: FontWeight.w400,
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: AppTextField(
                        borderColor: AppColors.primaryColor,
                        hintText: 'Current Password',
                        controller: currentpasswordController,
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                          } else {
                            return 'Enter Current Password';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 12.0),
                      child: Row(
                        children: [
                          const TextWidget(
                            text: 'New Password',
                            textcolor: AppColors.blackColor,
                            fontsize: 18,
                            fontweight: FontWeight.w400,
                          ),
                          TextWidget(
                            text: ' *',
                            textcolor: AppColors.red,
                            fontsize: 18,
                            fontweight: FontWeight.w400,
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: AppTextField(
                        borderColor: AppColors.primaryColor,
                        hintText: 'New Password',
                        controller: newpasswordController,
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                          } else {
                            return 'Enter New Password';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 12.0),
                      child: Row(
                        children: [
                          const TextWidget(
                            text: 'Confirm New Password',
                            textcolor: AppColors.blackColor,
                            fontsize: 18,
                            fontweight: FontWeight.w400,
                          ),
                          TextWidget(
                            text: ' *',
                            textcolor: AppColors.red,
                            fontsize: 18,
                            fontweight: FontWeight.w400,
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: AppTextField(
                        borderColor: AppColors.primaryColor,
                        hintText: 'Confirm New Password',
                        controller: confirmpasswordController,
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                          } else {
                            return 'Enter Confirm New Password';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: CommonButtonWidget(
                        text: 'Update Password',
                        onTap: () {
                          if (_formKey.currentState!.validate()) {
                            if (newpasswordController.text != confirmpasswordController.text) {
                              Utility.toast(message: "New Password and Confirm Password must be same");
                            } else {
                              changePasswordApi();
                            }
                          }
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
            ValueListenableBuilder<bool>(
              valueListenable: isLoading,
              builder: ((context, value, child) {
                if (value) return Utility.progress();
                return const SizedBox();
              }),
            )
          ],
        ),
      ),
    );
  }

  Future<void> changePasswordApi() async {
    final request = {
      'old_password': currentpasswordController.text,
      'new_password': newpasswordController.text,
      'new_password_confirmation': confirmpasswordController.text
    };
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;
      UserResponce userResponse =
          // ignore: use_build_context_synchronously
          UserResponce.fromJson(await ApiManager().postCall(AppString.changePasswordUrl, request, context));

      if (userResponse.status == '1') {
        Utility.toast(message: 'Password Changed Successfully');
        currentpasswordController.clear();
        confirmpasswordController.clear();
        newpasswordController.clear();
        isLoading.value = false;
      } else {
        isLoading.value = false;
        Utility.toast(message: userResponse.message!);
      }
    } else {
      Utility.toast(message: 'No Internet Connection');
    }
  }
}
