// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:trendi_people/model/category_model.dart';
import 'package:trendi_people/responce/category_responce.dart';
import 'package:trendi_people/screen/select_screen.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../model/user_model.dart';
import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/service_categories_widget.dart';
import '../widget/text_widget.dart';

class CreateRequestScreen extends StatefulWidget {
  const CreateRequestScreen({
    Key? key,
    required this.type,
    required this.gender,
    required this.title,
  }) : super(key: key);
  final String type;
  final String gender;
  final String title;

  @override
  State<CreateRequestScreen> createState() => _CreateRequestScreenState();
}

class _CreateRequestScreenState extends State<CreateRequestScreen> {
  int page = 0;
  int perPage = 10;
  bool stop = false;
  List<CategoryModel> categoryList = [];
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isPageLoading = ValueNotifier<bool>(false);
  UserModel? user;

  @override
  void initState() {
    super.initState();
    log(widget.type);
    log(widget.gender);
    getCategoryList(isFirst: true);
    fetchCustomerData();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  void noDataLogic(pageNumber) {
    page = pageNumber - 1;
    stop = true;
    _notify();
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(190.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 45,
                      width: 45,
                      child: Utility.imageLoader(
                        url: user?.profileImage ?? '',
                        isShapeCircular: true,
                        shape: BoxShape.circle,
                        placeholder: AppAssets.placeHolderImage,
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                TextWidget(
                  text: widget.title,
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Select the Category.',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 20),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      text: (widget.title == "Style me up")
                          ? 'How would you like to be Styled up?'
                          : (widget.title == "Alter")
                              ? 'What items would you like altered?'
                              : (widget.title == "Repair")
                                  ? 'What items would you like repaired?'
                                  : 'What items would you like create?',
                      fontsize: 25,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                    20.verticalSpace,
                    Stack(
                      children: [
                        GridView.builder(
                          padding: const EdgeInsets.only(bottom: 20),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: categoryList.length,
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: getValueForScreenType(context: context, mobile: 2, tablet: 3),
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                            mainAxisExtent: 175,
                            childAspectRatio: 0.75,
                          ),
                          itemBuilder: (context, index) {
                            return (categoryList.length - 1) == index
                                ? VisibilityDetector(
                                    key: Key(index.toString()),
                                    onVisibilityChanged: (VisibilityInfo info) {
                                      if (!stop &&
                                          index == (categoryList.length - 1) &&
                                          !isLoading.value &&
                                          !isPageLoading.value) {
                                        getCategoryList(isFirst: false);
                                      }
                                    },
                                    child: Stack(
                                      children: [
                                        categoryListWidget(index),
                                        ValueListenableBuilder<bool>(
                                          valueListenable: isPageLoading,
                                          builder: (context, value, _) {
                                            if (isPageLoading.value) {
                                              return Center(child: Utility.progress());
                                            }
                                            return const SizedBox();
                                          },
                                        )
                                      ],
                                    ),
                                  )
                                : categoryListWidget(index);
                          },
                        ),
                        if (!isLoading.value && categoryList.isEmpty) Utility.noDataWidget(text: 'No Category'),
                        ValueListenableBuilder(
                          valueListenable: isLoading,
                          builder: ((context, value, _) {
                            if (isLoading.value) {
                              return Utility.progress();
                            }
                            return const SizedBox();
                          }),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> getCategoryList({required bool isFirst}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    if (await ApiManager.checkInternet()) {
      page += 1;
      var request = <String, dynamic>{};
      request['page'] = page.toString();
      request['per_page'] = perPage.toString();
      request['type'] = widget.type;
      request['gender'] = widget.gender;
      CategoryResponce response = CategoryResponce.fromJson(
        await ApiManager().getCall(AppString.categoryListUrl, request, context),
      );
      if (response.status == "1" && response.data != null && response.data!.isNotEmpty) {
        categoryList.addAll(response.data!);

        _notify();
      } else {
        noDataLogic(page);
      }
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
    } else {
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
      Utility.toast(message: 'No Internet Connection');
    }
  }

  Widget categoryListWidget(index) {
    return ServiceCategoriesWidget(
      title: categoryList[index].name,
      image: widget.gender == 'men'
          ? categoryList[index].image
          : widget.gender == 'women'
              ? categoryList[index].femaleImage
              : categoryList[index].otherImage,
      // 'assets/image/ruler.png',
      background: AppColors.whiteColor,
      borderColor: AppColors.primaryColor,
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SelectServiceScreen(
                categoryName: categoryList[index].name ?? '',
                gender: widget.gender,
                type: widget.type,
                categoryId: categoryList[index].id.toString()),
          ),
        );
        log("message");
      },
    );
  }
}
