import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:trendi_people/responce/comman_response.dart';
import 'package:trendi_people/screen/change_password_screen.dart';
import 'package:trendi_people/screen/contact_us_screen.dart';
import 'package:trendi_people/screen/edit_profile_screen.dart';
import 'package:trendi_people/screen/sign_in_screen.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/firebase_messaging_services.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../model/user_model.dart';
import '../utility/api_manager.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/alert_dialog_box_widget.dart';
import '../widget/text_widget.dart';
import 'about_screen.dart';
import 'faq_screen.dart';
import 'how_its_work.dart';

class MyProfileScreen extends StatefulWidget {
  const MyProfileScreen({super.key});

  @override
  State<MyProfileScreen> createState() => _MyProfileScreenState();
}

class _MyProfileScreenState extends State<MyProfileScreen> {
  UserModel? user;
  @override
  void initState() {
    super.initState();

    fetchCustomerData();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(175.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(
              left: 25,
              right: 25,
              top: 20,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade100),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.arrow_back,
                      color: AppColors.black,
                    ),
                  ),
                ),
                10.verticalSpace,
                const TextWidget(
                  text: 'Your Profile',
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Your Profile and Setting ',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
              padding: const EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(right: 10),
                        height: 60,
                        width: 60,
                        child: Utility.imageLoader(
                          url: user?.profileImage ?? '',
                          isShapeCircular: true,
                          shape: BoxShape.circle,
                          placeholder: AppAssets.placeHolderImage,
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                              text: user?.firstName ?? "",
                              fontsize: 20,
                              textcolor: AppColors.blackColor,
                              fontweight: FontWeight.bold),
                          7.verticalSpace,
                          TextWidget(
                              text: user?.email ?? "",
                              fontsize: 15,
                              textcolor: AppColors.grey5,
                              fontweight: FontWeight.w400),
                        ],
                      )
                    ],
                  ),
                  10.verticalSpace,
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: AppColors.grey4),
                        color: AppColors.white24),
                    child: Column(
                      children: [
                        iconText(
                            icon: AppAssets.editIcon,
                            text: 'Edit Profile',
                            onTap: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const EditProfileScreen(),
                                  ));
                            }),
                        GestureDetector(
                            onTap: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const ChangePasswordScreen(),
                                  ));
                            },
                            child: iconText(icon: AppAssets.passwordIcon, text: 'Change Password')),
                        // iconText(icon: AppAssets.languageIcon, text: 'Language'),
                        // iconText(icon: AppAssets.clockIcon, text: 'Time Setting'),
                        // iconText(icon: AppAssets.locationIcon, text: 'My Location'),
                        // iconText(icon: AppAssets.paymentIcon, text: 'My Payments'),
                      ],
                    ),
                  ),
                  10.verticalSpace,
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: AppColors.grey4),
                        color: AppColors.white24),
                    child: Column(
                      children: [
                        GestureDetector(
                            onTap: () async {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const FAQScreen(),
                                  ));
                            },
                            child: iconText(icon: AppAssets.termsAndConditionIcon, text: 'FAQ')),
                        GestureDetector(
                            onTap: () async {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const AboutUsScreen(),
                                  ));
                            },
                            child: iconText(icon: AppAssets.privacyIcon, text: 'About')),
                        GestureDetector(
                            onTap: () async {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const HowItsWorksScreen(),
                                  ));
                            },
                            child: iconText(icon: AppAssets.male, text: 'How it Works')),
                        GestureDetector(
                            onTap: () async {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const ContactUsScreen(),
                                  ));
                            },
                            child: iconText(icon: AppAssets.shareIcon, text: 'Contact Us')),
                      ],
                    ),
                  ),
                  10.verticalSpace,
                  GestureDetector(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialogBox(
                          ontapNo: () {
                            Navigator.pop(context);
                          },
                          ontapYes: () {
                            logout(
                              onComplate: () {
                                Utility.clearPref();
                                Navigator.of(context).pushAndRemoveUntil(
                                  MaterialPageRoute(
                                    builder: (context) => const SignInScreen(),
                                  ),
                                  (route) => false,
                                );
                              },
                            );
                          },
                          fontSize: 16,
                          alertMessage: 'Are you sure want to Logout ?',
                          buttonTextYes: 'Yes',
                          buttonTextNo: 'No',
                          // icon: Icons.question_mark,
                          title: 'Logout',
                        ),
                      );
                    },
                    child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: AppColors.grey4),
                            color: AppColors.white24),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              Image.asset(
                                AppAssets.logoutIcon,
                                height: 24,
                                width: 24,
                              ),
                              5.horizontalSpace,
                              TextWidget(text: 'Log Out', textcolor: AppColors.red),
                            ],
                          ),
                        )),
                  )
                ],
              )),
        ),
      ),
    );
  }

  Widget iconText({onTap, icon, text}) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Image.asset(
              icon,
              height: 24,
              width: 24,
            ),
            15.horizontalSpace,
            TextWidget(text: text),
          ],
        ),
      ),
    );
  }

  Future<void> logout({Function()? onComplate}) async {
    if (await ApiManager.checkInternet()) {
      Map<String, dynamic> request = {'firebase_id': FirebaseMessagingService.token};
      CommonResponse userResponse =
          // ignore: use_build_context_synchronously
          CommonResponse.fromJson(await ApiManager().postCall(AppString.logoutUrl, request, context));

      if (userResponse.status == "1") {
        if (onComplate != null) onComplate();
        _notify();
      } else {
        Utility.toast(message: userResponse.message ?? '');
      }
    } else {
      Utility.toast(message: 'No Internet Connection');
    }
  }
}
