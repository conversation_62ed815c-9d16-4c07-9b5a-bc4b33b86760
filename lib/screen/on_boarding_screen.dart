import 'package:flutter/material.dart';
import 'package:trendi_people/screen/sign_in_screen.dart';
import 'package:trendi_people/screen/sign_up_screen.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_widget.dart';

class OnBordingHelloScreen extends StatelessWidget {
  const OnBordingHelloScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              color: AppColors.whiteColor,
              image: DecorationImage(fit: BoxFit.cover, image: AssetImage(AppAssets.onBoarding2)),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 25, right: 25, bottom: 50),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TextWidget(
                  text: 'Hello Shopper,',
                  textcolor: AppColors.whiteColor,
                  fontsize: 30,
                  letterSpacing: 0.5,
                ),
                20.verticalSpace,
                const TextWidget(
                  text: 'TRENDiPEOPLE help you Create, Tailor, Repair, Alter & style your clothes & accessories.',
                  textcolor: AppColors.whiteColor,
                  maxLines: 3,
                  fontweight: FontWeight.w400,
                  letterSpacing: 0.5,
                ),
                25.verticalSpace,
                Row(
                  children: [
                    Expanded(
                      child: CommonButtonWidget(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        border: Border.all(color: AppColors.primaryColor),
                        buttonColor: AppColors.transparent,
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SignUpScreen(),
                              ));
                        },
                        text: 'Sign up',
                      ),
                    ),
                    10.horizontalSpace,
                    Expanded(
                      child: CommonButtonWidget(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SignInScreen(),
                              ));
                        },
                        text: 'Login',
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
