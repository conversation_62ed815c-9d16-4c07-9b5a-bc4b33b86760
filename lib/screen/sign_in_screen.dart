import 'package:flutter/material.dart';
import 'package:trendi_people/screen/forgot_password_screen.dart';
import 'package:trendi_people/screen/home.dart';
import 'package:trendi_people/screen/sign_up_screen.dart';
import 'package:trendi_people/utility/firebase_messaging_services.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../responce/user_reponse.dart';
import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_field_widget.dart';
import '../widget/text_widget.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final rememberCheck = ValueNotifier<bool>(false);
  final _formKey = GlobalKey<FormState>();

  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Form(
          key: _formKey,
          child: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  color: AppColors.whiteColor,
                  image: DecorationImage(
                      fit: BoxFit.cover,
                      // colorFilter: ColorFilter.mode(
                      //     Colors.black.withOpacity(1.0), BlendMode.dstATop),
                      image: AssetImage("assets/image/signin.png")),
                ),
              ),
              SingleChildScrollView(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        100.verticalSpace,
                        const TextWidget(
                          text: 'Sign in',
                          textcolor: AppColors.whiteColor,
                          fontsize: 30,
                          letterSpacing: 0.5,
                        ),
                        15.verticalSpace,
                        const TextWidget(
                          text: 'Please fill in your information',
                          textcolor: AppColors.whiteColor,
                          maxLines: 2,
                          fontweight: FontWeight.w300,
                          fontsize: 16,
                          letterSpacing: 0.5,
                        ),
                        30.verticalSpace,
                        AppTextField(
                          inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                          controller: emailController,
                          hintText: 'Email',
                          preffixIcon: Padding(
                            padding: const EdgeInsets.only(left: 25, right: 20),
                            child: Image.asset(
                              AppAssets.mailIcon,
                              color: AppColors.whiteColor,
                              height: 24,
                              width: 24,
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter your email';
                            } else {
                              if (!RegExp(
                                      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
                                  .hasMatch(value)) {
                                return 'Enter valid email';
                              }
                            }

                            return null;
                          },
                        ),
                        15.verticalSpace,
                        AppTextField(
                          obscureText: true,
                          controller: passwordController,
                          inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                          hintText: 'Password',
                          preffixIcon: Padding(
                            padding: const EdgeInsets.only(left: 25, right: 20),
                            child: Image.asset(
                              AppAssets.passwordIcon,
                              color: AppColors.whiteColor,
                              height: 24,
                              width: 24,
                            ),
                          ),
                          validator: (value) {
                            if (value != null && value.trim() != '') {
                            } else {
                              return 'Please enter password ';
                            }
                            return null;
                          },
                        ),
                        25.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const ForgotPassWordScreen(),
                                    ));
                              },
                              child: const TextWidget(
                                text: 'Forgot Password',
                                textcolor: AppColors.primaryColor,
                                fontsize: 14,
                                fontweight: FontWeight.w400,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ],
                        ),
                        // ValueListenableBuilder(
                        //   valueListenable: rememberCheck,
                        //   builder: (context, value, _) {
                        //     return GestureDetector(
                        //       onTap: () {
                        //         rememberCheck.value = !rememberCheck.value;
                        //       },
                        //       child: Row(
                        //         children: [
                        //           Icon(
                        //             (value) ? Icons.check_box : Icons.check_box_outline_blank_sharp,
                        //             color: Colors.white54,
                        //             size: 30,
                        //           ),
                        //           10.horizontalSpace,
                        //           const TextWidget(
                        //             text: 'Remember me',
                        //             textcolor: AppColors.whiteColor,
                        //             fontweight: FontWeight.w400,
                        //             fontsize: 14,
                        //           )
                        //         ],
                        //       ),
                        //     );
                        //   },
                        // ),
                        25.verticalSpace,
                        CommonButtonWidget(
                            onTap: () {
                              if (_formKey.currentState!.validate()) {
                                getLogin();
                              }
                            },
                            text: 'Sign In'),
                        15.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const TextWidget(
                              text: '''Don't have an account yet?''',
                              textcolor: AppColors.whiteColor,
                              fontsize: 14,
                              letterSpacing: 0.5,
                              fontweight: FontWeight.w400,
                            ),
                            3.horizontalSpace,
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const SignUpScreen(),
                                    ));
                              },
                              child: const TextWidget(
                                text: 'Sign up',
                                textcolor: AppColors.primaryColor,
                                fontsize: 14,
                                fontweight: FontWeight.w400,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  void getLogin() async {
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;
      var request = <String, dynamic>{};
      request['email'] = emailController.text;
      request['password'] = passwordController.text;
      request['role'] = 'user';
      request["firebase_id"] = FirebaseMessagingService.token;

      UserResponce userResponse =
          // ignore: use_build_context_synchronously
          UserResponce.fromJson(await ApiManager().postCall(AppString.loginUrl, request, context));
      if (userResponse.status == "1" && userResponse.data != null) {
        Utility.setPref(
          key: AppString.tokenKey,
          value: userResponse.token.toString(),
        );

        Utility.setUser(userResponse.data!);
        // ignore: use_build_context_synchronously
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => const Home(),
          ),
          (Route<dynamic> route) => false,
        );
      }
      Utility.toast(message: userResponse.message);

      isLoading.value = false;
    } else {
      Utility.toast(message: 'No Internet');
    }
  }
}
