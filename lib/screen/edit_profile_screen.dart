import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:trendi_people/responce/user_reponse.dart';
import 'package:trendi_people/screen/home.dart';
import 'package:trendi_people/screen/sign_in_screen.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/alert_dialog_box_widget.dart';

import '../model/user_model.dart';
import '../responce/comman_response.dart';
import '../utility/api_manager.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_field_widget.dart';
import '../widget/text_widget.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  final emailController = TextEditingController();
  final bioController = TextEditingController();
  final fullNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  final phoneNumberController = TextEditingController();
  UserModel? user;
  ValueNotifier<File?> profileImageFile = ValueNotifier<File?>(null);
  ValueNotifier<String?> profileImage = ValueNotifier<String?>('');
  @override
  void initState() {
    super.initState();
    fetchCustomerData();
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");
    fullNameController.text = user?.firstName ?? "";
    lastNameController.text = user?.lastName ?? "";
    emailController.text = user?.email ?? "";
    if (user?.phoneNumber != null) phoneNumberController.text = user?.phoneNumber ?? "";
    bioController.text = user?.bio ?? "";
    profileImage.value = user!.profileImage ?? '';

    _notify();
  }

  Future<void> editUser() async {
    final request = {
      'first_name': fullNameController.text,
      'last_name': lastNameController.text,
      'email': emailController.text.trim(),
      'phone_number': phoneNumberController.text,
      'bio': bioController.text,
    };
    final List<MapEntry<String, File>> files = [
      if (profileImageFile.value != null) MapEntry('profile_image', profileImageFile.value!)
    ];
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;

      UserResponce registerResponse = UserResponce.fromJson(
        await ApiManager().multipartRequest(url: AppString.editUserUrl, request: request, files: files),
      );
      if (registerResponse.status == '1' && registerResponse.data != null) {
        Utility.setUser(registerResponse.data!);

        Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const Home(),
            ));

        isLoading.value = false;
      } else {
        isLoading.value = false;
        Utility.toast(message: registerResponse.message);
      }
    } else {
      Utility.toast(message: 'No internet connection');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(180.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(
              left: 25,
              right: 25,
              top: 20,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade100),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.arrow_back,
                      color: AppColors.black,
                    ),
                  ),
                ),
                20.verticalSpace,
                const TextWidget(
                  text: 'Edit Profile',
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Your profile and settings',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
        body: Stack(
          children: [
            Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 25),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      20.verticalSpace,
                      Row(
                        children: [
                          ValueListenableBuilder<String?>(
                              valueListenable: profileImage,
                              builder: (BuildContext context, value, _) {
                                if (value != null && value.isNotEmpty) {
                                  return Stack(
                                    children: [
                                      Container(
                                        height: 120,
                                        width: 120,
                                        decoration: const BoxDecoration(
                                          color: Colors.black,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Utility.imageLoader(
                                            url: value,
                                            placeholder: AppAssets.cameraIcon,
                                            isShapeCircular: true,
                                            shape: BoxShape.circle),
                                      ),
                                      Positioned(
                                        height: 120,
                                        right: 42,
                                        child: GestureDetector(
                                          onTap: () async {
                                            final image = await ImagePicker().pickImage(source: ImageSource.gallery);
                                            if (image?.path != null) {
                                              profileImage.value = null;
                                              profileImageFile.value = File(image!.path);
                                            }
                                          },
                                          child: SizedBox(
                                            height: 24,
                                            width: 24,
                                            child: Image.asset(AppAssets.cameraIcon),
                                          ),
                                        ),
                                      ),
                                      // Positioned(
                                      //   top: 5,
                                      //   left: 70,
                                      //   right: 0,
                                      //   child: GestureDetector(
                                      //     onTap: () {
                                      //
                                      //     },
                                      //     child: const Icon(
                                      //       Icons.cancel_outlined,
                                      //       color: Colors.black,
                                      //     ),
                                      //   ),
                                      // )
                                    ],
                                  );
                                }
                                return ValueListenableBuilder<File?>(
                                  valueListenable: profileImageFile,
                                  builder: (BuildContext context, value, _) {
                                    if (value != null) {
                                      return Stack(
                                        children: [
                                          Container(
                                            height: 120,
                                            width: 120,
                                            decoration: BoxDecoration(
                                              color: Colors.black,
                                              shape: BoxShape.circle,
                                              // borderRadius: BorderRadius.circular(10),
                                              image: DecorationImage(
                                                image: FileImage(value),
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                          Container(
                                              height: 120,
                                              width: 120,
                                              decoration:
                                                  const BoxDecoration(shape: BoxShape.circle, color: Colors.black45)),
                                          Positioned(
                                            height: 120,
                                            left: 55,
                                            child: GestureDetector(
                                              onTap: () async {
                                                final image =
                                                    await ImagePicker().pickImage(source: ImageSource.gallery);
                                                profileImage.value = null;
                                                if (image?.path != null) {
                                                  profileImageFile.value = File(image!.path);
                                                }
                                              },
                                              child: SizedBox(
                                                height: 24,
                                                width: 24,
                                                child: Image.asset(AppAssets.cameraIcon),
                                              ),
                                            ),
                                          ),
                                        ],
                                      );
                                    }
                                    return GestureDetector(
                                      onTap: () {
                                        profileImageFile.value == null ? getImage(false) : null;
                                      },
                                      child: Container(
                                          height: 120,
                                          width: 120,
                                          decoration: BoxDecoration(
                                            color: AppColors.grey2,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Center(
                                            child: TextWidget(
                                              text: 'Add Profile \nPicture',
                                              textAlign: TextAlign.center,
                                              fontsize: 14,
                                              fontweight: FontWeight.normal,
                                              textcolor: AppColors.grey8,
                                            ),
                                          )),
                                    );
                                  },
                                );
                              }),
                          const Spacer(),
                          Flexible(
                            child: CommonButtonWidget(
                              innerPaddding: const EdgeInsets.all(15),
                              maxwidth: 120,
                              text: 'Delete my account',
                              buttonColor: AppColors.primaryBgColor,
                              textColor: AppColors.black,
                              border: Border.all(color: AppColors.primaryColor),
                              onTap: () {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return AlertDialogBox(
                                        ontapYes: () {
                                          deleteApi(user?.id ?? 0);
                                        },
                                        alertMessage: 'You’ll receive an email from us in 48 hours confirming deletion',
                                        buttonTextYes: 'DELETE',
                                        title: 'Are you sure you want to delete ?',
                                        ontapNo: () {
                                          Navigator.of(context).pop();
                                        },
                                        buttonTextNo: 'Cancle');
                                  },
                                );
                              },
                            ),
                          )
                        ],
                      ),
                      // Stack(
                      //   children: [
                      //     Container(
                      //       margin: const EdgeInsets.only(right: 10),
                      //       height: 120,
                      //       width: 120,
                      //       decoration: const BoxDecoration(
                      //           shape: BoxShape.circle,
                      //           image: DecorationImage(image: AssetImage("assets/image/tiger.jpg"), scale: 2)),
                      //     ),
                      //     Container(
                      //         height: 120,
                      //         width: 120,
                      //         decoration: const BoxDecoration(shape: BoxShape.circle, color: Colors.black45)),
                      //     Positioned(
                      //       height: 120,
                      //       left: 55,
                      //       child: SizedBox(
                      //         height: 24,
                      //         width: 24,
                      //         child: Image.asset(AppAssets.cameraIcon),
                      //       ),
                      //     )
                      //   ],
                      // ),
                      30.verticalSpace,
                      // Row(
                      //   children: [
                      //     const TextWidget(
                      //       text: 'Gwen Stacy',
                      //       fontsize: 30,
                      //       overflow: TextOverflow.ellipsis,
                      //       maxLines: 2,
                      //     ),
                      //     10.horizontalSpace,
                      //     SizedBox(
                      //       height: 24,
                      //       width: 24,
                      //       child: Image.asset(AppAssets.editIcon),
                      //     ),
                      //   ],
                      // ),
                      // 10.verticalSpace,
                      AppTextField(
                        inputTextStyle: const TextStyle(color: AppColors.blackColor),
                        controller: fullNameController,
                        borderColor: AppColors.primaryColor,
                        hintText: 'First Name',
                        hintStyle: TextStyle(color: AppColors.grey5),
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                          } else {
                            return 'Please enter your first name';
                          }
                          return null;
                        },
                        preffixIcon: Padding(
                          padding: const EdgeInsets.only(left: 25, right: 20),
                          child: Image.asset(
                            AppAssets.profileIcon,
                            color: AppColors.blackColor,
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ),
                      15.verticalSpace,
                      AppTextField(
                        inputTextStyle: const TextStyle(color: AppColors.blackColor),
                        controller: lastNameController,
                        borderColor: AppColors.primaryColor,
                        hintText: 'Last Name',
                        hintStyle: TextStyle(color: AppColors.grey5),
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                          } else {
                            return 'Please enter your last name';
                          }
                          return null;
                        },
                        preffixIcon: Padding(
                          padding: const EdgeInsets.only(left: 25, right: 20),
                          child: Image.asset(
                            AppAssets.profileIcon,
                            color: AppColors.blackColor,
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ),
                      15.verticalSpace,
                      AppTextField(
                        borderColor: AppColors.primaryColor,
                        inputTextStyle: TextStyle(color: AppColors.grey5),
                        controller: emailController,
                        readOnly: true,
                        hintText: 'Email',
                        hintStyle: TextStyle(color: AppColors.grey5),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter your email';
                          } else {
                            if (!RegExp(
                                    r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
                                .hasMatch(value)) {
                              return 'Enter valid email';
                            }
                          }

                          return null;
                        },
                        preffixIcon: Padding(
                          padding: const EdgeInsets.only(left: 25, right: 20),
                          child: Image.asset(
                            AppAssets.mailIcon,
                            color: AppColors.blackColor,
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ),
                      15.verticalSpace,
                      AppTextField(
                        borderColor: AppColors.primaryColor,
                        inputTextStyle: const TextStyle(color: AppColors.blackColor),
                        controller: phoneNumberController,
                        hintText: 'Phone',
                        hintStyle: TextStyle(color: AppColors.grey5),
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                            if (!RegExp(r'^-?(([0-9]*)|(([0-9]*)\.([0-9]*)))$').hasMatch(value)) {
                              return 'Enter valid mobile number';
                            }
                          } else {
                            return 'Enter mobile number';
                          }
                          return null;
                        },
                        preffixIcon: Padding(
                          padding: const EdgeInsets.only(left: 25, right: 20),
                          child: Image.asset(
                            AppAssets.phoneIcon,
                            color: AppColors.blackColor,
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ),
                      15.verticalSpace,
                      AppTextField(
                        borderColor: AppColors.primaryColor,
                        inputTextStyle: const TextStyle(color: AppColors.blackColor),
                        controller: bioController,
                        maxLines: 3,
                        minLines: 3,
                        hintText: 'Bio',
                        hintStyle: TextStyle(color: AppColors.grey5),
                        preffixIcon: Padding(
                          padding: const EdgeInsets.only(left: 25, right: 20),
                          child: Image.asset(
                            AppAssets.bioIcon,
                            color: AppColors.blackColor,
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ),
                      CommonButtonWidget(
                        innerPaddding: const EdgeInsets.all(15),
                        padding: const EdgeInsets.all(10),
                        borderRadius: BorderRadius.circular(12),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        border: Border.all(color: AppColors.primaryColor),
                        buttonColor: AppColors.primaryColor,
                        textColor: AppColors.blackColor,
                        onTap: () {
                          if (_formKey.currentState!.validate()) {
                            editUser();
                          }
                        },
                        text: 'Save',
                      ),
                    ],
                  ),
                ),
              ),
            ),
            ValueListenableBuilder(
              valueListenable: isLoading,
              builder: ((context, value, _) {
                if (isLoading.value) {
                  return Container(
                      color: AppColors.whiteColor,
                      height: MediaQuery.of(context).size.height,
                      child: Utility.progress());
                }
                return const SizedBox();
              }),
            ),
          ],
        ),
      ),
    );
  }

  void getImage(bool camera) async {
    final image = await ImagePicker().pickImage(
      source: camera ? ImageSource.camera : ImageSource.gallery,
    );

    if (image != null) {
      profileImageFile.value = File(image.path);
    } else {
      log('Error');
    }
    _notify();
  }

  Future<void> deleteApi(int id) async {
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;

      CommonResponse response = CommonResponse.fromJson(await ApiManager().deleteCall(
        AppString.delereUserUrl(id),
      ));
      if (response.status == "1") {
        Utility.clearPref();
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => const SignInScreen(),
          ),
          (route) => false,
        );

        isLoading.value = false;
      } else {
        isLoading.value = false;
        Utility.toast(message: response.message);
      }
    } else {
      isLoading.value = false;
      Utility.toast(message: 'No Internet Connection');
    }
  }
}
