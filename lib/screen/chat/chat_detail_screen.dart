// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:developer';
import 'dart:io';

import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:trendi_people/model/category_model_for_chat_screen.dart';
import 'package:trendi_people/model/chat_user_model.dart';
import 'package:trendi_people/model/user_model.dart';
import 'package:trendi_people/screen/chat/chat_payment_screen.dart';
import 'package:trendi_people/screen/chat/chat_payment_view.dart';
import 'package:trendi_people/screen/chat/chat_text_message_item_view.dart';
import 'package:trendi_people/utility/api_manager.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/app_colors.dart';
import 'package:trendi_people/utility/app_string.dart';
import 'package:trendi_people/utility/firebase_chat_service.dart';
import 'package:trendi_people/utility/utility.dart';
import 'package:trendi_people/widget/text_field_widget.dart';

class ChatDetailScreen extends StatefulWidget {
  const ChatDetailScreen({
    Key? key,
    this.otherUserProfile,
    this.otherUserName,
    required this.otherUserId,
  }) : super(key: key);
  final String? otherUserProfile;
  final String? otherUserName;
  final int otherUserId;
  @override
  State<ChatDetailScreen> createState() => ChatDetailScreenState();
}

class ChatDetailScreenState extends State<ChatDetailScreen> {
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isStackLoading = ValueNotifier<bool>(false);

  UserModel? loginUser;
  final messagecontroller = TextEditingController();
  final chatController = ScrollController();
  List<CategoryModelForChatScreen> serviceCategoryType = <CategoryModelForChatScreen>[
    CategoryModelForChatScreen(name: 'Make', keyValue: 'MAKE'),
    CategoryModelForChatScreen(name: 'Repair', keyValue: 'REPAIR'),
    CategoryModelForChatScreen(name: 'Alter', keyValue: 'ALTER'),
    CategoryModelForChatScreen(name: 'Style me up', keyValue: 'STYLE_ME_UP'),
  ];

  @override
  void initState() {
    super.initState();
    getCurrentUser();
  }

  Future<void> getCurrentUser() async {
    isLoading.value = true;
    loginUser = await Utility.getUser();

    isLoading.value = false;
  }

  Future<bool> _onWillPop() async {
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(width: 0.08),
              ),
            ),
            child: AppBar(
              elevation: 0,
              titleSpacing: 0,
              shadowColor: AppColors.transparent,
              backgroundColor: AppColors.whiteColor,
              leading: IconButton(
                onPressed: () {
                  Navigator.pop(context, true);
                },
                icon: Icon(
                  size: 25,
                  color: AppColors.black,
                  Icons.arrow_back,
                ),
              ),
              title: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(40),
                    child: SizedBox(
                      width: 50,
                      height: 50,
                      child: Utility.imageLoader(
                        url: widget.otherUserProfile ?? '',
                        context: context,
                        fit: BoxFit.cover,
                        placeholder: AppAssets.logo,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 15,
                  ),
                  Flexible(
                    child: GestureDetector(
                      onTap: () {},
                      child: Text(
                        '${widget.otherUserName}',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 20,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        bottomSheet: ColoredBox(
          color: AppColors.whiteColor,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  // color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(50),
                ),
                padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                child: Row(
                  children: [
                    Flexible(
                      child: AppTextField(
                        controller: messagecontroller,
                        keyboardType: TextInputType.text,
                        hintText: 'Write your message',
                        borderColor: AppColors.primaryColor,
                        hintStyle: TextStyle(
                          color: AppColors.grey4,
                        ),
                        suffixIcon: GestureDetector(
                          onTap: () async {
                            XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);
                            if (image?.path != null) {
                              isStackLoading.value = true;
                              final file = File(image?.path ?? '');
                              final timeInMilisecond = DateTime.now().millisecondsSinceEpoch.toString();
                              final downloadUrl =
                                  await FireaseChatService.uploadFile(file: file, filepath: timeInMilisecond);

                              if (downloadUrl != null) {
                                sendMessage(photo: downloadUrl);
                              }
                            }
                          },
                          child: Container(
                            width: 50,
                            height: 50,
                            margin: const EdgeInsets.all(4),
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10)),
                            child: Center(
                              child: Icon(
                                size: 20,
                                color: AppColors.black,
                                Icons.attach_file,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        if (isStackLoading.value) return;
                        if (messagecontroller.text.isNotEmpty) {
                          sendMessage();
                        }
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        margin: const EdgeInsets.all(4),
                        height: 50,
                        width: 50,
                        child: const Center(
                          child: Icon(
                            Icons.send,
                            color: AppColors.primaryColor,
                            size: 30,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        body: ValueListenableBuilder<bool>(
          valueListenable: isLoading,
          builder: (_, value, __) {
            if (value) {
              return Center(child: Utility.progress());
            }
            return Stack(
              alignment: Alignment.center,
              children: [
                FirestoreListView<ChatUserModel>(
                  query: FireaseChatService.getMessagesQuery(
                    uniqueKey: FireaseChatService.generateUniqueKey(
                      userId1: (widget.otherUserId),
                      userId2: (loginUser?.id ?? 0),
                    ),
                  ),
                  padding: const EdgeInsets.only(bottom: 90),
                  pageSize: 20,
                  reverse: true,
                  controller: chatController,
                  emptyBuilder: (context) => const Center(child: Text('No data')),
                  errorBuilder: (context, error, stackTrace) => Text(error.toString()),
                  loadingBuilder: (context) => Center(child: Utility.progress()),
                  itemBuilder: (context, doc) {
                    final chatData = doc.data();
                    FireaseChatService.messagesreadupdatebadgevaluetozero(FireaseChatService.generateUniqueKey(
                      userId1: (widget.otherUserId),
                      userId2: (loginUser?.id ?? 0),
                    ));

                    if (chatData.chatType == AppString.chatMessageTypePayment) {
                      log(widget.otherUserId.toString());

                      return ChatPaymentView(
                        isOtherUser: chatData.senderId == (widget.otherUserId).toString(),
                        time: chatData.formmetedTime,
                        gender: chatData.gender,
                        category:
                            serviceCategoryType.firstWhere((element) => element.keyValue == chatData.category).name,
                        serviceName: chatData.serviceName,
                        subCategoryName: chatData.subCategoryName,
                        description: chatData.description,
                        paymentStatus: chatData.paymentStatus,
                        onPayTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ChatPaymentScreen(
                                firebseMessageId: doc.id,
                                orderId: chatData.orderId,
                                orderPrice: chatData.amount,
                              ),
                            ),
                          );
                        },
                      );
                    }
                    return ChatTextMessageView(
                      message: chatData.message ?? '',
                      time: chatData.formmetedTime,
                      imageUrl: chatData.imageUrl,
                      isOtherUser: chatData.senderId == (widget.otherUserId).toString(),
                    );
                  },
                ),
                ValueListenableBuilder<bool>(
                  valueListenable: isStackLoading,
                  builder: (_, value, __) {
                    if (value) {
                      return Center(child: Utility.progress());
                    }
                    return const SizedBox();
                  },
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Future<void> sendMessage({String? photo}) async {
    isStackLoading.value = true;
    final message = messagecontroller.text.trim();
    messagecontroller.clear();
    await FireaseChatService.enterDatainUsersChatListCollection(
      useraId: (loginUser?.id ?? 0).toString(),
      imageUrl: photo,
      userbId: (widget.otherUserId).toString(),
      groupchatid: FireaseChatService.generateUniqueKey(
        userId1: (widget.otherUserId),
        userId2: (loginUser?.id ?? 0),
      ),
      message: message,
      myid: (loginUser?.id ?? 0).toString(),
    );
    await FireaseChatService.enterChatsData(
      imageUrl: photo,
      myId: (loginUser?.id ?? 0).toString(),
      otherId: (widget.otherUserId).toString(),
      groupChatId: FireaseChatService.generateUniqueKey(
        userId1: (widget.otherUserId),
        userId2: (loginUser?.id ?? 0),
      ),
      content: message,
    );
    isStackLoading.value = false;
    await sendNotification(message);
  }

  Future<void> sendNotification(String message) async {
    if (await ApiManager.checkInternet()) {
      var request = <String, dynamic>{};
      request['user_id'] = widget.otherUserId.toString();
      request['message'] = message.trim();
      // ignore: use_build_context_synchronously
      await ApiManager().postCall(
        AppString.sendNotification,
        request,
        context,
      );
    }
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }
}
