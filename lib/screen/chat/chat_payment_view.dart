// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:trendi_people/utility/app_colors.dart';
import 'package:trendi_people/utility/app_string.dart';

class ChatPaymentView extends StatelessWidget {
  const ChatPaymentView({
    Key? key,
    required this.isOtherUser,
    required this.time,
    this.gender,
    this.category,
    this.subCategoryName,
    this.serviceName,
    this.description,
    this.paymentStatus,
    required this.onPayTap,
  }) : super(key: key);

  final bool isOtherUser;
  final String time;
  final String? gender;
  final String? category;
  final String? subCategoryName;
  final String? serviceName;
  final String? description;
  final String? paymentStatus;
  final Function() onPayTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        mainAxisAlignment: isOtherUser ? MainAxisAlignment.start : MainAxisAlignment.end,
        children: [
          Container(
            padding: const EdgeInsets.only(
              left: 16,
              right: 10,
              top: 12,
              bottom: 12,
            ),
            constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.7),
            decoration: BoxDecoration(
              color: isOtherUser ? null : AppColors.primaryColor,
              borderRadius: BorderRadius.only(
                bottomLeft: isOtherUser ? Radius.zero : const Radius.circular(12),
                topLeft: const Radius.circular(12),
                topRight: const Radius.circular(12),
                bottomRight: isOtherUser ? const Radius.circular(12) : Radius.zero,
              ),
              border: isOtherUser ? Border.all(color: AppColors.primaryColor) : null,
            ),
            margin: const EdgeInsets.only(
              top: 8,
              bottom: 8,
            ),
            child: Column(
              crossAxisAlignment: isOtherUser ? CrossAxisAlignment.start : CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Payment Request',
                  style: TextStyle(
                    color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(
                  height: 6,
                ),
                if (gender != null)
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'Gender: ',
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      TextSpan(
                        text: gender,
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontSize: 12,
                        ),
                      ),
                    ]),
                  ),
                if (category != null)
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'Category: ',
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      TextSpan(
                        text: category,
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontSize: 12,
                        ),
                      ),
                    ]),
                  ),
                if (subCategoryName != null)
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'SubCategory: ',
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      TextSpan(
                        text: subCategoryName,
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontSize: 12,
                        ),
                      ),
                    ]),
                  ),
                if (serviceName != null)
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'Service: ',
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      TextSpan(
                        text: serviceName,
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontSize: 12,
                        ),
                      ),
                    ]),
                  ),
                if (description != null)
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'Description: ',
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      TextSpan(
                        text: description,
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontSize: 12,
                        ),
                      ),
                    ]),
                  ),
                const SizedBox(
                  height: 6,
                ),
                if (paymentStatus != null)
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'Payment Status: ',
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      TextSpan(
                        text: paymentStatus,
                        style: TextStyle(
                          color: isOtherUser ? AppColors.blackColor : AppColors.whiteColor,
                          fontSize: 12,
                        ),
                      ),
                    ]),
                  ),
                const SizedBox(
                  height: 6,
                ),
                if (paymentStatus != AppString.chatMessageTypeSuccess)
                  InkWell(
                    onTap: onPayTap,
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        'Pay Now',
                        style: TextStyle(
                          color: AppColors.whiteColor,
                        ),
                      ),
                    ),
                  ),
                if (time.trim() != '') ...[
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    time,
                    style: TextStyle(
                        fontWeight: FontWeight.w800,
                        color:
                            isOtherUser ? AppColors.blackColor.withOpacity(0.5) : AppColors.whiteColor.withOpacity(0.5),
                        fontSize: 10),
                  )
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
