import 'package:flutter/material.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/app_colors.dart';
import 'package:trendi_people/utility/utility.dart';
import 'package:trendi_people/widget/image_screen.dart';

class ChatTextMessageView extends StatelessWidget {
  final String message;
  final String time;
  final bool isOtherUser;
  final String? anotherUserName;
  final String? imageUrl;
  const ChatTextMessageView({
    Key? key,
    required this.message,
    required this.time,
    required this.isOtherUser,
    this.anotherUserName,
    this.imageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        mainAxisAlignment: isOtherUser ? MainAxisAlignment.start : MainAxisAlignment.end,
        children: [
          Container(
            padding: const EdgeInsets.only(
              left: 16,
              right: 10,
              top: 12,
              bottom: 12,
            ),
            constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.7),
            decoration: BoxDecoration(
                color: isOtherUser ? AppColors.primaryColor : null,
                borderRadius: BorderRadius.only(
                  bottomLeft: isOtherUser ? Radius.zero : const Radius.circular(12),
                  topLeft: const Radius.circular(12),
                  topRight: const Radius.circular(12),
                  bottomRight: isOtherUser ? const Radius.circular(12) : Radius.zero,
                ),
                border: isOtherUser ? null : Border.all(color: AppColors.primaryColor)),
            margin: const EdgeInsets.only(
              top: 8,
              bottom: 8,
            ),
            child: Column(
              crossAxisAlignment: isOtherUser ? CrossAxisAlignment.start : CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (imageUrl != null && imageUrl?.trim() != '')
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => ImageView(imageUrl: imageUrl),
                        ),
                      );
                    },
                    child: SizedBox(
                      height: 150,
                      width: 200,
                      child: Utility.imageLoader(
                        context: context,
                        fit: BoxFit.fitWidth,
                        shape: BoxShape.rectangle,
                        url: imageUrl!,
                        placeholder: AppAssets.logo,
                      ),
                    ),
                  ),
                if (message.trim().isNotEmpty) ...[
                  Text(
                    message,
                    style: TextStyle(
                      color: isOtherUser ? AppColors.whiteColor : AppColors.blackColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
                if (time.trim() != '') ...[
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    time,
                    style: TextStyle(
                        fontWeight: FontWeight.w800,
                        color:
                            isOtherUser ? AppColors.whiteColor.withOpacity(0.5) : AppColors.blackColor.withOpacity(0.5),
                        fontSize: 10),
                  )
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
