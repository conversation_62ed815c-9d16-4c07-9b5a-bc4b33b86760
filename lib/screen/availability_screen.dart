import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:trendi_people/model/pricing_model.dart';
import 'package:trendi_people/responce/pricing_responce.dart';
import 'package:trendi_people/screen/payment_screen.dart';
import 'package:trendi_people/screen/service_submit_screen.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/app_drop_down_widget.dart';

import '../model/active_leaves_model.dart';
import '../model/available_days_model.dart';
import '../model/category_model.dart';
import '../model/freelancer_detail_model.dart';
import '../model/user_model.dart';
import '../utility/api_manager.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_widget.dart';

List<String> urgencyForDelivery = <String>[
  "I'm Flexible",
  "As soon as possible",
];

class AvailabilityScreen extends StatefulWidget {
  const AvailabilityScreen(
      {super.key,
      this.activeList,
      required this.availableDays,
      required this.userId,
      this.catergoryList,
      required this.isFormOnly,
      this.freelancerDetailModel,
      this.gender});
  final List<ActiveLeaves>? activeList;
  final AvailableDays? availableDays;
  final int userId;
  final List<CategoryModel>? catergoryList;
  final bool isFormOnly;
  final String? gender;

  final FreelancerDetailModel? freelancerDetailModel;
  // final List<selectedCategory>? selectedCategory;

  @override
  State<AvailabilityScreen> createState() => _AvailabilityScreenState();
}

class _AvailabilityScreenState extends State<AvailabilityScreen> {
  final startTime = TextEditingController();
  DateTime today = DateTime.now();
  String deliveryOptionVAlue = '0';
  PricingResponce? pricindResponce;
  String dropdownvalue = "I'm Flexible";
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);

  List<PricingModel> pricingModel = [];
  UserModel? user;

  @override
  void initState() {
    super.initState();
    fetchCustomerData();
    getPricingDetail();
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(190.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 45,
                      width: 45,
                      child: Utility.imageLoader(
                        url: user?.profileImage ?? '',
                        isShapeCircular: true,
                        shape: BoxShape.circle,
                        placeholder: AppAssets.placeHolderImage,
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                TextWidget(
                  text: " ${widget.freelancerDetailModel?.firstName}'s  Availability",
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TextWidget(
                    text: 'What delivery and collection method would you prefer?',
                    maxLines: 2,
                    fontsize: 15,
                    fontweight: FontWeight.w500),
                SizedBox(
                  height: 35,
                  child: RadioListTile(
                    dense: true,
                    activeColor: AppColors.black,
                    value: '0',
                    groupValue: deliveryOptionVAlue,
                    contentPadding: EdgeInsets.zero,
                    title: const TextWidget(
                      text: '(FREE) You meet with the TRENDiPERSON',
                      fontweight: FontWeight.normal,
                      maxLines: 2,
                      textcolor: AppColors.blackColor,
                      fontsize: 14,
                    ),
                    onChanged: (String? newValue) {
                      deliveryOptionVAlue = newValue ?? "";
                      getPricingDetail();
                      _notify();
                      print(newValue);
                    },
                  ),
                ),
                if (widget.isFormOnly) ...[
                  10.verticalSpace,
                  SizedBox(
                    height: 35,
                    child: RadioListTile(
                      dense: true,
                      activeColor: AppColors.black,
                      value: '1',
                      groupValue: deliveryOptionVAlue,
                      contentPadding: EdgeInsets.zero,
                      title: const TextWidget(
                        text: '(STANDARD) We pick up & return in 3 days',
                        fontweight: FontWeight.normal,
                        maxLines: 2,
                        textcolor: AppColors.blackColor,
                        fontsize: 14,
                      ),
                      onChanged: (String? newValue) {
                        deliveryOptionVAlue = newValue ?? "";
                        getPricingDetail();
                        _notify();
                        print(deliveryOptionVAlue);
                      },
                    ),
                  ),
                  10.verticalSpace,
                  SizedBox(
                    height: 35,
                    child: RadioListTile(
                      dense: true,
                      value: '2',
                      groupValue: deliveryOptionVAlue,
                      contentPadding: EdgeInsets.zero,
                      activeColor: AppColors.black,
                      title: const TextWidget(
                        text: '(SAME DAY SERVICE) We pick up & return',
                        fontweight: FontWeight.normal,
                        maxLines: 2,
                        textcolor: AppColors.blackColor,
                        fontsize: 14,
                      ),
                      onChanged: (String? newValue) {
                        deliveryOptionVAlue = newValue ?? "";
                        getPricingDetail();
                        _notify();
                        print(deliveryOptionVAlue);
                      },
                    ),
                  ),
                ],
                20.verticalSpace,
                const TextWidget(
                    text: 'What delivery and collection method would you prefer?',
                    maxLines: 2,
                    fontsize: 15,
                    fontweight: FontWeight.w500),
                10.verticalSpace,
                AppDropDown(
                  selectedValue: dropdownvalue,
                  items: urgencyForDelivery
                      .map((e) => DropdownMenuItem(
                            value: e,
                            child: Text(
                              e,
                            ),
                          ))
                      .toList(),
                  onSelect: (String? newvalueSelected) {
                    dropdownvalue = newvalueSelected!;
                  },
                ),
                20.verticalSpace,

                TextWidget(
                    text: "Pick a date to book ${widget.freelancerDetailModel?.firstName}'s Service",
                    maxLines: 2,
                    fontsize: 15,
                    fontweight: FontWeight.w500),
                10.verticalSpace,

                Container(
                  // margin: const EdgeInsets.all(10),
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                  width: MediaQuery.of(context).size.width,
                  // height: MediaQuery.of(context).size.height / 1.78,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: AppColors.grey4),
                      color: AppColors.white24),
                  child: TableCalendar(
                    locale: "en_US",
                    availableGestures: AvailableGestures.none,
                    calendarStyle: CalendarStyle(
                      defaultDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          shape: BoxShape.rectangle,
                          color: AppColors.primaryColor),
                      isTodayHighlighted: true,
                      weekendDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          shape: BoxShape.rectangle,
                          color: AppColors.primaryColor),
                      weekendTextStyle: TextStyle(color: AppColors.black),
                      disabledDecoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: AppColors.grey2,
                      ),
                      markerDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          shape: BoxShape.rectangle,
                          color: AppColors.primaryColor),
                      holidayDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5), shape: BoxShape.rectangle, color: AppColors.grey3),
                      selectedDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5), color: AppColors.black, shape: BoxShape.rectangle),
                      // rowDecoration: BoxDecoration(
                      //     borderRadius: BorderRadius.circular(5),
                      //     shape: BoxShape.rectangle,
                      //     color: AppColors.primaryColor),
                      outsideDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          shape: BoxShape.rectangle,
                          color: AppColors.primaryColor),
                      withinRangeDecoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          shape: BoxShape.rectangle,
                          color: AppColors.primaryColor),
                      selectedTextStyle: const TextStyle(color: Colors.white),
                      todayDecoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: AppColors.primaryColor.withOpacity(1),
                        shape: BoxShape.rectangle,
                      ),
                    ),
                    enabledDayPredicate: (date) {
                      final enableDays = <int>[];
                      if (widget.availableDays?.monday == 1) {
                        enableDays.add(1);
                      }
                      if (widget.availableDays?.tuesday == 1) {
                        enableDays.add(2);
                      }
                      if (widget.availableDays?.wednesday == 1) {
                        enableDays.add(3);
                      }
                      if (widget.availableDays?.thursday == 1) {
                        enableDays.add(4);
                      }

                      if (widget.availableDays?.friday == 1) {
                        enableDays.add(5);
                      }
                      if (widget.availableDays?.saturday == 1) {
                        enableDays.add(6);
                      }
                      if (widget.availableDays?.sunday == 1) {
                        enableDays.add(7);
                      }
                      final disable = widget.activeList?.any((element) =>
                              (element.startDate?.isBefore(date) ?? true) && (element.endDate?.isAfter(date) ?? true) ||
                              dateInOnlyDate(element.startDate) == dateInOnlyDate(date) ||
                              dateInOnlyDate(element.endDate) == dateInOnlyDate(date)) ??
                          false;

                      return enableDays.contains(date.weekday) &&
                          !disable &&
                          dateInOnlyDate(DateTime.now()) != dateInOnlyDate(date);
                    },
                    onDaySelected: _onDaySelected,
                    selectedDayPredicate: (day) {
                      return isSameDay(day, today);
                    },
                    headerStyle: const HeaderStyle(
                      formatButtonVisible: false,
                      titleCentered: true,
                    ),
                    daysOfWeekVisible: true,
                    focusedDay: today,
                    firstDay: DateTime.now(),
                    calendarFormat: CalendarFormat.month,
                    lastDay: DateTime.utc(2030, 10, 16),
                  ),
                ),
                10.verticalSpace,
                // Container(
                //   margin: const EdgeInsets.all(10),
                //   padding: const EdgeInsets.all(20),
                //   width: MediaQuery.of(context).size.width,
                //   height: MediaQuery.of(context).size.height / 6,
                //   decoration: BoxDecoration(
                //       borderRadius: BorderRadius.circular(20),
                //       border: Border.all(color: AppColors.grey4),
                //       color: AppColors.white24),
                //   child: Column(
                //     crossAxisAlignment: CrossAxisAlignment.start,
                //     mainAxisAlignment: MainAxisAlignment.start,
                //     children: [
                //       TextWidget(
                //         text: '10th January',
                //         fontweight: FontWeight.normal,
                //         textcolor: AppColors.grey5,
                //       ),
                //       10.verticalSpace,
                //       Row(
                //         children: [
                //           const Icon(
                //             Icons.check_circle_outline_outlined,
                //             color: AppColors.primaryColor,
                //           ),
                //           5.horizontalSpace,
                //           const TextWidget(
                //             text: '9:00 AM - 1:00 PM',
                //           ),
                //         ],
                //       ),
                //       5.verticalSpace,
                //       Row(
                //         children: [
                //           const Icon(
                //             Icons.circle,
                //             color: AppColors.primaryColor,
                //           ),
                //           5.horizontalSpace,
                //           const TextWidget(
                //             text: '4:00 PM - 7:00 PM',
                //           ),
                //         ],
                //       ),
                //     ],
                //   ),
                // ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    textCoontainer(AppColors.primaryColor, 'Available'),
                    textCoontainer(AppColors.black, 'Selected'),
                  ],
                ),
                20.verticalSpace,
                if (widget.isFormOnly)
                  Container(
                    // margin: const EdgeInsets.all(10),
                    padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: AppColors.grey4),
                        color: AppColors.white24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const TextWidget(text: 'Order Details', maxLines: 2),
                        10.verticalSpace,
                        Row(
                          children: [
                            Expanded(
                                child: TextWidget(
                              text: widget.catergoryList?.first.name ?? '',
                              maxLines: 2,
                              fontsize: 14,
                              fontweight: FontWeight.normal,
                            )),
                            TextWidget(
                                text: '£${pricindResponce?.totalCost}',
                                maxLines: 2,
                                fontsize: 14,
                                fontweight: FontWeight.normal),
                          ],
                        ),
                        5.verticalSpace,
                        // Row(
                        //   children: [
                        //     const Expanded(
                        //         child: TextWidget(
                        //             text: 'Service fee', maxLines: 2, fontsize: 14, fontweight: FontWeight.normal)),
                        //     TextWidget(
                        //         text: '£${pricindResponce?.serviceCharges}',
                        //         maxLines: 2,
                        //         fontsize: 14,
                        //         fontweight: FontWeight.normal),
                        //   ],
                        // ),
                        // 5.verticalSpace,
                        Row(
                          children: [
                            const Expanded(
                                child: TextWidget(
                                    text: 'Delivery fee', maxLines: 2, fontsize: 14, fontweight: FontWeight.normal)),
                            TextWidget(
                                text: '£${pricindResponce?.deliveryCharges}',
                                maxLines: 2,
                                fontsize: 14,
                                fontweight: FontWeight.normal),
                          ],
                        ),
                        5.verticalSpace,
                        Divider(
                          height: 1,
                          color: AppColors.grey4,
                        ),
                        5.verticalSpace,
                        Row(
                          children: [
                            const Expanded(child: TextWidget(text: 'Total cost', maxLines: 2)),
                            TextWidget(text: '£${pricindResponce?.totalPayableCost}', maxLines: 2),
                          ],
                        ),
                      ],
                    ),
                  ),
                20.verticalSpace,
                if (widget.freelancerDetailModel?.address != null) ...[
                  const TextWidget(text: "Address", maxLines: 2, fontsize: 15, fontweight: FontWeight.w500),
                  10.verticalSpace,
                  TextWidget(
                    text: widget.freelancerDetailModel?.address ?? "",
                    fontweight: FontWeight.normal,
                    maxLines: 2,
                    textcolor: AppColors.blackColor,
                    fontsize: 14,
                  ),
                  20.verticalSpace,
                ],
                if (widget.freelancerDetailModel?.latitude != null &&
                    widget.freelancerDetailModel?.longitude != '') ...[
                  const TextWidget(text: "Location", maxLines: 2, fontsize: 15, fontweight: FontWeight.w500),
                  10.verticalSpace,
                  SizedBox(
                    height: 200,
                    width: MediaQuery.of(context).size.width,
                    child: Utility.imageLoader(
                      url: Utility.getMapUrl(
                          widget.freelancerDetailModel?.latitude ?? '', widget.freelancerDetailModel?.longitude ?? ''),
                      placeholder: AppAssets.backHomeIcon,
                      fit: BoxFit.cover,
                      context: context,
                    ),
                  ),
                  20.verticalSpace,
                ],

                widget.isFormOnly
                    ? CommonButtonWidget(
                        text: 'Next: Payment',
                        onTap: () {
                          String formattedDate = DateFormat('dd-MM-yyyy').format(today);
                          log(formattedDate);
                          if (formattedDate != DateFormat('dd-MM-yyyy').format(DateTime.now())) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PaymentScreen(
                                    price: pricindResponce?.totalPayableCost,
                                    gender: widget.gender,
                                    deliveryOption: deliveryOption,
                                    startDate: formattedDate,
                                    catergoryList: widget.catergoryList ?? [],
                                    freelancerDetailModel: widget.freelancerDetailModel,
                                    aviblityStatus: dropdownvalue),
                              ),
                            );
                          }
                          // showDialog(
                          //   context: context,
                          //   builder: (BuildContext context) {
                          //     return AlertDialog(
                          //         insetPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                          //         shape: RoundedRectangleBorder(
                          //           borderRadius: BorderRadius.circular(20),
                          //         ),
                          //         title: Row(
                          //           mainAxisAlignment: MainAxisAlignment.end,
                          //           children: [
                          //             GestureDetector(
                          //               onTap: () {
                          //                 Navigator.pop(context);
                          //               },
                          //               child: Container(
                          //                 margin: const EdgeInsets.all(10),
                          //                 decoration: BoxDecoration(
                          //                     border: Border.all(color: AppColors.blackColor),
                          //                     shape: BoxShape.circle,
                          //                     color: AppColors.whiteColor),
                          //                 child: const Icon(
                          //                   Icons.close,
                          //                   color: AppColors.blackColor,
                          //                 ),
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //         titlePadding: const EdgeInsets.only(left: 10, top: 10),
                          //         // contentPadding: const EdgeInsets.all(25),
                          //         actionsPadding: const EdgeInsets.symmetric(horizontal: 25),
                          //         // insetPadding: const EdgeInsets.all(25),
                          //         actions: [
                          //           Column(
                          //             crossAxisAlignment: CrossAxisAlignment.center,
                          //             mainAxisAlignment: MainAxisAlignment.center,
                          //             mainAxisSize: MainAxisSize.min,
                          //             children: [
                          //               Container(
                          //                 height: 60,
                          //                 width: 60,
                          //                 decoration: const BoxDecoration(
                          //                     shape: BoxShape.circle,
                          //                     image: DecorationImage(
                          //                         image: AssetImage("assets/image/tiger.jpg"), scale: 2)),
                          //               ),
                          //               8.verticalSpace,
                          //               TextWidget(
                          //                 text: widget.freelancerDetailModel?.firstName ?? "",
                          //                 fontsize: 30,
                          //                 overflow: TextOverflow.ellipsis,
                          //                 maxLines: 2,
                          //               ),
                          //               10.verticalSpace,
                          //               TextWidget(
                          //                 text: "£${pricindResponce?.totalPayableCost}",
                          //                 fontsize: 15,
                          //                 overflow: TextOverflow.ellipsis,
                          //               ),
                          //               // 10.verticalSpace,
                          //               // TextWidget(
                          //               //   text: "View Invoice",
                          //               //   textDecoration: TextDecoration.underline,
                          //               //   fontsize: 14,
                          //               //   textcolor: AppColors.grey8,
                          //               //   overflow: TextOverflow.ellipsis,
                          //               //   fontweight: FontWeight.w400,
                          //               // ),
                          //               30.verticalSpace,
                          //               const TextWidget(
                          //                 text: "Select Payment Method",
                          //                 fontsize: 18,
                          //                 overflow: TextOverflow.ellipsis,
                          //               ),
                          //               CommonButtonWidget(
                          //                 innerPaddding: const EdgeInsets.all(15),
                          //                 padding: const EdgeInsets.all(10),
                          //                 borderRadius: BorderRadius.circular(12),
                          //                 fontSize: 12,
                          //                 fontWeight: FontWeight.w600,
                          //                 border: Border.all(color: AppColors.primaryColor),
                          //                 buttonColor: AppColors.primaryBgColor,
                          //                 textColor: AppColors.black,
                          //                 onTap: () {
                          //                   String formattedDate = DateFormat('dd-MM-yyyy').format(today);
                          //                   log(formattedDate);
                          //                   if (formattedDate != DateFormat('dd-MM-yyyy').format(DateTime.now())) {
                          //                     Navigator.push(
                          //                       context,
                          //                       MaterialPageRoute(
                          //                         builder: (context) => PaymentScreen(
                          //                             price: pricindResponce?.totalPayableCost,
                          //                             gender: widget.gender,
                          //                             deliveryOption: deliveryOption,
                          //                             startDate: formattedDate,
                          //                             catergoryList: widget.catergoryList ?? [],
                          //                             freelancerDetailModel: widget.freelancerDetailModel,
                          //                             aviblityStatus: dropdownvalue),
                          //                       ),
                          //                     );
                          //                   } else {
                          //                     Utility.toast(message: 'Please Select Date');
                          //                   }
                          //                 },
                          //                 text: 'Continue with Google Pay',
                          //                 image: AppAssets.googleIcon,
                          //               ),
                          //               CommonButtonWidget(
                          //                 innerPaddding: const EdgeInsets.all(15),
                          //                 padding: const EdgeInsets.all(10),
                          //                 borderRadius: BorderRadius.circular(12),
                          //                 fontSize: 12,
                          //                 fontWeight: FontWeight.w600,
                          //                 border: Border.all(color: AppColors.primaryColor),
                          //                 buttonColor: AppColors.primaryBgColor,
                          //                 textColor: AppColors.black,
                          //                 onTap: () {
                          //                   String formattedDate = DateFormat('dd-MM-yyyy').format(today);
                          //                   log(formattedDate);
                          //                   if (formattedDate != DateFormat('dd-MM-yyyy').format(DateTime.now())) {
                          //                     Navigator.push(
                          //                       context,
                          //                       MaterialPageRoute(
                          //                         builder: (context) => PaymentScreen(
                          //                             price: pricindResponce?.totalPayableCost,
                          //                             gender: widget.gender,
                          //                             deliveryOption: deliveryOption,
                          //                             startDate: formattedDate,
                          //                             catergoryList: widget.catergoryList ?? [],
                          //                             freelancerDetailModel: widget.freelancerDetailModel,
                          //                             aviblityStatus: dropdownvalue),
                          //                       ),
                          //                     );
                          //                   } else {
                          //                     Utility.toast(message: 'Please Select Date');
                          //                   }
                          //                 },
                          //                 text: 'Continue with Apple Pay',
                          //                 image: AppAssets.appleIcon,
                          //               ),
                          //               CommonButtonWidget(
                          //                 innerPaddding: const EdgeInsets.all(15),
                          //                 padding: const EdgeInsets.all(10),
                          //                 borderRadius: BorderRadius.circular(12),
                          //                 fontSize: 12,
                          //                 fontWeight: FontWeight.w600,
                          //                 border: Border.all(color: AppColors.primaryColor),
                          //                 buttonColor: AppColors.primaryBgColor,
                          //                 textColor: AppColors.black,
                          //                 onTap: () {
                          //                   String formattedDate = DateFormat('dd-MM-yyyy').format(today);
                          //                   log(formattedDate);
                          //                   if (formattedDate != DateFormat('dd-MM-yyyy').format(DateTime.now())) {
                          //                     Navigator.push(
                          //                       context,
                          //                       MaterialPageRoute(
                          //                         builder: (context) => PaymentScreen(
                          //                             price: pricindResponce?.totalPayableCost,
                          //                             gender: widget.gender,
                          //                             deliveryOption: deliveryOption,
                          //                             startDate: formattedDate,
                          //                             catergoryList: widget.catergoryList ?? [],
                          //                             freelancerDetailModel: widget.freelancerDetailModel,
                          //                             aviblityStatus: dropdownvalue),
                          //                       ),
                          //                     );
                          //                   } else {
                          //                     Utility.toast(message: 'Please Select Date');
                          //                   }
                          //                 },
                          //                 text: 'Card Payment',
                          //                 image: AppAssets.paymentIcon,
                          //               ),
                          //               30.verticalSpace
                          //             ],
                          //           ),
                          //         ]);
                          //   },
                          // );
                        },
                      )
                    : CommonButtonWidget(
                        text: ' Next: Place Order ',
                        onTap: () {
                          String formattedDate = DateFormat('dd-MM-yyyy').format(today);
                          log(formattedDate);
                          if (formattedDate != DateFormat('dd-MM-yyyy').format(DateTime.now())) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ServiceSubmitScreen(
                                      deliveryOption: deliveryOption,
                                      startDate: formattedDate,
                                      catergoryList: widget.catergoryList ?? [],
                                      freelancerDetailModel: widget.freelancerDetailModel,
                                      aviblityStatus: dropdownvalue),
                                ));
                          } else {
                            Utility.toast(message: 'Please Select Date');
                          }
                        },
                      ),

                // const Spacer(),
                // CommonButtonWidget(
                //     padding: const EdgeInsets.all(10),
                //     onTap: () {
                //       showDialog(
                //         context: context,
                //         builder: (BuildContext context) {
                //           return AlertDialog(
                //               shape: RoundedRectangleBorder(
                //                 borderRadius: BorderRadius.circular(20),
                //               ),
                //               title: Row(
                //                 mainAxisAlignment: MainAxisAlignment.end,
                //                 children: [
                //                   GestureDetector(
                //                     onTap: () {
                //                       Navigator.pop(context);
                //                     },
                //                     child: Container(
                //                       margin: const EdgeInsets.all(10),
                //                       decoration: BoxDecoration(
                //                           border: Border.all(color: AppColors.blackColor),
                //                           shape: BoxShape.circle,
                //                           color: AppColors.whiteColor),
                //                       child: const Icon(
                //                         Icons.close,
                //                         color: AppColors.blackColor,
                //                       ),
                //                     ),
                //                   ),
                //                 ],
                //               ),
                //               titlePadding: const EdgeInsets.only(left: 10, top: 10),
                //               // contentPadding: const EdgeInsets.all(25),
                //               actionsPadding: const EdgeInsets.symmetric(horizontal: 25),
                //               // insetPadding: const EdgeInsets.all(25),
                //               actions: [
                //                 Column(
                //                   crossAxisAlignment: CrossAxisAlignment.center,
                //                   mainAxisAlignment: MainAxisAlignment.center,
                //                   mainAxisSize: MainAxisSize.min,
                //                   children: [
                //                     Container(
                //                       height: 60,
                //                       width: 60,
                //                       decoration: const BoxDecoration(
                //                           shape: BoxShape.circle,
                //                           image:
                //                               DecorationImage(image: AssetImage("assets/image/tiger.jpg"), scale: 2)),
                //                     ),
                //                     8.verticalSpace,
                //                     const TextWidget(
                //                       text: "Gwen Stacy",
                //                       fontsize: 30,
                //                       overflow: TextOverflow.ellipsis,
                //                       maxLines: 2,
                //                     ),
                //                     10.verticalSpace,
                //                     const TextWidget(
                //                       text: "\$522.00",
                //                       fontsize: 15,
                //                       overflow: TextOverflow.ellipsis,
                //                     ),
                //                     10.verticalSpace,
                //                     TextWidget(
                //                       text: "View Invoice",
                //                       textDecoration: TextDecoration.underline,
                //                       fontsize: 14,
                //                       textcolor: AppColors.grey8,
                //                       overflow: TextOverflow.ellipsis,
                //                       fontweight: FontWeight.w400,
                //                     ),
                //                     30.verticalSpace,
                //                     const TextWidget(
                //                       text: "Select Payment Method",
                //                       fontsize: 18,
                //                       overflow: TextOverflow.ellipsis,
                //                     ),
                //                     CommonButtonWidget(
                //                       innerPaddding: const EdgeInsets.all(15),
                //                       padding: const EdgeInsets.all(10),
                //                       borderRadius: BorderRadius.circular(12),
                //                       fontSize: 12,
                //                       fontWeight: FontWeight.w600,
                //                       border: Border.all(color: AppColors.primaryColor),
                //                       buttonColor: AppColors.primaryBgColor,
                //                       textColor: AppColors.black,
                //                       onTap: () {
                //                         Navigator.push(
                //                             context,
                //                             MaterialPageRoute(
                //                               builder: (context) => const BookingConformSCreen(),
                //                             ));
                //                       },
                //                       text: 'Continue with Google Pay',
                //                       image: AppAssets.paymentIcon,
                //                     ),
                //                     CommonButtonWidget(
                //                       innerPaddding: const EdgeInsets.all(15),
                //                       padding: const EdgeInsets.all(10),
                //                       borderRadius: BorderRadius.circular(12),
                //                       fontSize: 12,
                //                       fontWeight: FontWeight.w600,
                //                       border: Border.all(color: AppColors.primaryColor),
                //                       buttonColor: AppColors.primaryBgColor,
                //                       textColor: AppColors.black,
                //                       onTap: () {
                //                         Navigator.push(
                //                             context,
                //                             MaterialPageRoute(
                //                               builder: (context) => const BookingConformSCreen(),
                //                             ));
                //                       },
                //                       text: 'Continue with Payment GateWay',
                //                       image: AppAssets.paymentIcon,
                //                     ),
                //                     30.verticalSpace
                //                   ],
                //                 ),
                //               ]);
                //         },
                //       );
                //     },
                //     // onTap: () {
                //     //   Navigator.push(
                //     //       context,
                //     //       MaterialPageRoute(
                //     //         builder: (context) => const BookingConformSCreen(),
                //     //       ));
                //     // },
                //     text: 'Booking'),

                10.verticalSpace
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget textCoontainer(color, text) {
    return Row(children: [
      Container(
        height: 10,
        width: 20,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: color,
        ),
      ),
      10.horizontalSpace,
      TextWidget(text: text)
    ]);
  }

  DateTime? dateInOnlyDate(DateTime? date) {
    if (date != null) {
      return DateTime(date.year, date.month, date.day);
    }
    return null;
  }

  void _onDaySelected(DateTime day, DateTime focuseDay) {
    today = day;
    log('${today}adjbasgffygayfyas');
    _notify();
  }

  getPricingDetail() async {
    log('in aPi');
    isLoading.value = true;
    if (await ApiManager.checkInternet()) {
      var request = <String, dynamic>{};
      if (widget.catergoryList!.isNotEmpty) {
        for (int i = 0; i < widget.catergoryList!.length; i++) {
          request['categories[$i]'] = widget.catergoryList![i].id.toString();
        }
      }
      request['delivery_option'] = deliveryOption;

      PricingResponce response =
          PricingResponce.fromJson(await ApiManager().getCall(AppString.pricingUrl(widget.userId), request, context));
      if (response.status == "1" && response.data != null) {
        // pricingModel.addAll(response.data!);
        pricindResponce = response;

        isLoading.value = false;
        _notify();
      } else {
        isLoading.value = false;
        return Utility.toast(message: response.message);
      }
    } else {
      isLoading.value = false;
      return Utility.toast(message: 'No Internet Connection');
    }
  }

  String? get deliveryOption {
    if (deliveryOptionVAlue == '0') {
      return 'FREE';
    } else if (deliveryOptionVAlue == '1') {
      return 'STANDARD';
    } else if (deliveryOptionVAlue == '2') {
      return 'SAME_DAY_SERVICE';
    }
    return null;
  }
}
