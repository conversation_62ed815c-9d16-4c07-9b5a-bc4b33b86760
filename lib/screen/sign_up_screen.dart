import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:trendi_people/screen/sign_in_screen.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../responce/comman_response.dart';
import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_field_widget.dart';
import '../widget/text_widget.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final fullNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final confirmController = TextEditingController();
  final phoneNumberController = TextEditingController();
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  final _formKey = GlobalKey<FormState>();
  ValueNotifier<bool> chekedBoxValue = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Form(
          key: _formKey,
          child: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  color: AppColors.whiteColor,
                  image: DecorationImage(
                      fit: BoxFit.cover,
                      // colorFilter: ColorFilter.mode(
                      //     Colors.black.withOpacity(1.0), BlendMode.dstATop),
                      image: AssetImage("assets/image/signup.png")),
                ),
              ),
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const TextWidget(
                          text: 'Sign up',
                          textcolor: AppColors.whiteColor,
                          fontsize: 30,
                          letterSpacing: 0.5,
                        ),
                        15.verticalSpace,
                        const TextWidget(
                          text: 'Please fill in your details',
                          textcolor: AppColors.whiteColor,
                          maxLines: 2,
                          fontweight: FontWeight.w300,
                          fontsize: 16,
                          letterSpacing: 0.5,
                        ),
                        30.verticalSpace,
                        AppTextField(
                          inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                          controller: fullNameController,
                          hintText: 'First Name',
                          validator: (value) {
                            if (value != null && value.trim() != '') {
                            } else {
                              return 'Please enter your First name';
                            }
                            return null;
                          },
                          preffixIcon: Padding(
                            padding: const EdgeInsets.only(left: 25, right: 20),
                            child: Image.asset(
                              AppAssets.profileIcon,
                              color: AppColors.whiteColor,
                              height: 24,
                              width: 24,
                            ),
                          ),
                        ),
                        15.verticalSpace,
                        AppTextField(
                          inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                          controller: lastNameController,
                          hintText: 'Last Name',
                          validator: (value) {
                            if (value != null && value.trim() != '') {
                            } else {
                              return 'Please enter your Last name';
                            }
                            return null;
                          },
                          preffixIcon: Padding(
                            padding: const EdgeInsets.only(left: 25, right: 20),
                            child: Image.asset(
                              AppAssets.profileIcon,
                              color: AppColors.whiteColor,
                              height: 24,
                              width: 24,
                            ),
                          ),
                        ),
                        15.verticalSpace,
                        AppTextField(
                          inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                          controller: emailController,
                          hintText: 'Email',
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter your email';
                            } else {
                              if (!RegExp(
                                      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
                                  .hasMatch(value)) {
                                return 'Enter valid email';
                              }
                            }

                            return null;
                          },
                          preffixIcon: Padding(
                            padding: const EdgeInsets.only(left: 25, right: 20),
                            child: Image.asset(
                              AppAssets.mailIcon,
                              color: AppColors.whiteColor,
                              height: 24,
                              width: 24,
                            ),
                          ),
                        ),
                        15.verticalSpace,
                        // AppTextField(
                        //   inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                        //   controller: phoneNumberController,
                        //   hintText: 'Phone',
                        //   keyboardType: TextInputType.phone,
                        //   validator: (value) {
                        //     if (value != null && value.trim() != '') {
                        //       if (!RegExp(r'^-?(([0-9]*)|(([0-9]*)\.([0-9]*)))$').hasMatch(value)) {
                        //         return 'Enter valid mobile number';
                        //       }
                        //     } else {
                        //       return 'Enter mobile number';
                        //     }
                        //     return null;
                        //   },
                        //   preffixIcon: Padding(
                        //     padding: const EdgeInsets.only(left: 25, right: 20),
                        //     child: Image.asset(
                        //       AppAssets.phoneIcon,
                        //       color: AppColors.whiteColor,
                        //       height: 24,
                        //       width: 24,
                        //     ),
                        //   ),
                        // ),
                        // 15.verticalSpace,
                        AppTextField(
                          obscureText: true,
                          controller: passwordController,
                          inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                          hintText: 'Password',
                          validator: (value) {
                            if (value != null && value.trim() != '') {
                            } else {
                              return 'Please enter password ';
                            }
                            return null;
                          },
                          preffixIcon: Padding(
                            padding: const EdgeInsets.only(left: 25, right: 20),
                            child: Image.asset(
                              AppAssets.passwordIcon,
                              color: AppColors.whiteColor,
                              height: 24,
                              width: 24,
                            ),
                          ),
                        ),
                        15.verticalSpace,
                        AppTextField(
                          obscureText: true,
                          inputTextStyle: const TextStyle(color: AppColors.whiteColor),
                          controller: confirmController,
                          hintText: 'Confirm Password',
                          validator: (value) {
                            if (value != null && value.trim() != '') {
                            } else {
                              return 'Please enter confirm password';
                            }
                            return null;
                          },
                          preffixIcon: Padding(
                            padding: const EdgeInsets.only(left: 25, right: 20),
                            child: Image.asset(
                              AppAssets.passwordIcon,
                              color: AppColors.whiteColor,
                              height: 24,
                              width: 24,
                            ),
                          ),
                        ),
                        30.verticalSpace,
                        CommonButtonWidget(
                            onTap: () {
                              if (_formKey.currentState!.validate()) {
                                if (chekedBoxValue.value == true) {
                                  createUser();
                                } else {
                                  return Utility.toast(message: 'Please Accept Terms & Condition');
                                }
                              }
                            },
                            text: 'Sign up'),
                        15.verticalSpace,
                        Padding(
                          padding: const EdgeInsets.fromLTRB(15, 10, 15, 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ValueListenableBuilder<bool>(
                                valueListenable: chekedBoxValue,
                                builder: (context, value, _) {
                                  return GestureDetector(
                                    onTap: () {
                                      chekedBoxValue.value = !chekedBoxValue.value;
                                    },
                                    child: Icon(
                                      value ? Icons.check_box : Icons.check_box_outline_blank,
                                      size: 20,
                                      color: AppColors.primaryColor,
                                    ),
                                  );
                                },
                              ),
                              Flexible(
                                child: RichText(
                                  textAlign: TextAlign.center,
                                  text: TextSpan(
                                    children: [
                                      const TextSpan(
                                        text: "By signing up you agree to our ",
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.whiteColor,
                                          fontSize: 16,
                                        ),
                                      ),
                                      TextSpan(
                                        text: 'Terms',
                                        style: const TextStyle(
                                            color: AppColors.primaryColor, fontSize: 16, fontWeight: FontWeight.w900),
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () async {
                                            const url = 'https://www.trendipeople.com/terms/';
                                            if (await canLaunchUrlString(url)) {
                                              await launchUrlString(url);
                                            }
                                          },
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        15.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const TextWidget(
                              text: '''Already have an account?''',
                              textcolor: AppColors.whiteColor,
                              fontsize: 14,
                              fontweight: FontWeight.w400,
                              letterSpacing: 0.5,
                            ),
                            3.horizontalSpace,
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const SignInScreen(),
                                    ));
                              },
                              child: const TextWidget(
                                text: 'Sign in',
                                textcolor: AppColors.primaryColor,
                                fontsize: 14,
                                fontweight: FontWeight.w400,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ],
                        ),
                        15.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  createUser() async {
    final request = <String, String>{
      'first_name': fullNameController.text,
      'last_name': lastNameController.text,
      'email': emailController.text,
      'password': passwordController.text,
      // 'phone_number': phoneNumberController.text,
      // 'bio': 'descriptionController.text',
    };
    // final List<MapEntry<String, File>> files = [
    //   if (profileImageFile.value != null) MapEntry('profile_image', profileImageFile.value!)
    // ];
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;

      CommonResponse response = CommonResponse.fromJson(await ApiManager().multipartRequest(
        url: AppString.signupUrl, request: request,
        //files: files
      ));
      if (response.status == "1") {
        isLoading.value = false;
        // ignore: use_build_context_synchronously
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const SignInScreen(),
          ),
        );
        return Utility.toast(message: response.message);
      } else {
        isLoading.value = false;
        return Utility.toast(message: response.message);
      }
    } else {
      isLoading.value = false;
      return Utility.toast(message: 'No Internet Connection');
    }
  }
}
