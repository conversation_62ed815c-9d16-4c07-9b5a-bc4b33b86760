import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_share/flutter_share.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:trendi_people/model/files_model.dart';
import 'package:trendi_people/model/freelancer_detail_model.dart';
import 'package:trendi_people/model/review_list_model.dart';
import 'package:trendi_people/responce/freeliancer_detail_responce.dart';
import 'package:trendi_people/responce/report_response.dart';
import 'package:trendi_people/responce/review_list_reponce.dart';
import 'package:trendi_people/screen/availability_screen.dart';
import 'package:trendi_people/screen/view_full_image_page.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../model/category_model.dart';
import '../model/freeliancer_model.dart';
import '../model/skills_model.dart';
import '../utility/api_manager.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/alert_dialog_box_widget.dart';
import '../widget/comman_button_widget.dart';
import '../widget/freliancer_list_widget.dart';
import '../widget/text_widget.dart';
import 'chat/chat_detail_screen.dart';

class FreeliancerDetailScreen extends StatefulWidget {
  const FreeliancerDetailScreen(
      {super.key,
      required this.freeliancerId,
      this.catergoryList,
      required this.isFormOnly,
      this.gender,
      required this.freeliancerModel});
  final int freeliancerId;
  final List<CategoryModel>? catergoryList;
  final bool isFormOnly;
  final String? gender;
  final FreeliancerModel freeliancerModel;

  @override
  State<FreeliancerDetailScreen> createState() => _FreeliancerDetailScreenState();
}

class _FreeliancerDetailScreenState extends State<FreeliancerDetailScreen> {
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  FreelancerDetailModel? freelancerDetailModel;
  List<Skills> selectedcategoryList = [];
  List<ReviewListModel> reviewList = [];
  ValueNotifier<Skills?> isSelectedSkill = ValueNotifier<Skills?>(null);
  String rating = '';
  int page = 0;
  int perPage = 10;
  bool stop = false;

  ValueNotifier<bool> isPageLoading = ValueNotifier<bool>(false);
  final reportController = TextEditingController();

  @override
  void initState() {
    super.initState();
    getFreeLiancerDetail();
    getFreeliancerList(isFirst: true);
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  void noDataLogic(pageNumber) {
    page = pageNumber - 1;
    stop = true;
    _notify();
  }

  Future<void> share(String? id) async {
    await FlutterShare.share(
      title: 'TrendiPeople',
      linkUrl: 'https://trendipeople.com/services/men/make/makers/$id/',
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(95.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 20, right: 20, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (context) {
                              return AlertDialogBox(
                                  ontapYes: () {
                                    if (reportController.text.trim().isEmpty) {
                                      Utility.toast(message: 'Please enter something');
                                    } else {
                                      reportApi();
                                      Navigator.pop(context);
                                    }
                                  },
                                  isTextField: true,
                                  controller: reportController,
                                  alertMessage: 'Are You sure you want to report this user?',
                                  buttonTextYes: 'Report',
                                  title: 'Report user',
                                  ontapNo: () {
                                    Navigator.of(context).pop();
                                  },
                                  buttonTextNo: 'Cancel');
                            },
                          );
                        },
                        child: const Padding(
                          padding: EdgeInsets.all(10.0),
                          child: Icon(Icons.report),
                        )),
                    GestureDetector(
                        onTap: () {
                          share(freelancerDetailModel?.id.toString());
                        },
                        child: const Padding(
                          padding: EdgeInsets.all(10.0),
                          child: Icon(Icons.share),
                        )),
                    // Flexible(
                    //   child: CommonButtonWidget(
                    //     innerPaddding: const EdgeInsets.all(15),
                    //     padding: const EdgeInsets.all(10),
                    //     fontSize: 16,
                    //     maxwidth: 150,
                    //     fontWeight: FontWeight.w600,
                    //     border: Border.all(color: AppColors.primaryColor),
                    //     buttonColor: AppColors.primaryColor,
                    //     onTap: () {
                    //       share(freelancerDetailModel?.id.toString());
                    //     },
                    //     text: 'Share Profile',
                    //   ),
                    // ),
                    // Flexible(
                    //   child: CommonButtonWidget(
                    //     innerPaddding: const EdgeInsets.all(15),
                    //     maxwidth: 120,
                    //     text: 'Delete my account',
                    //     buttonColor: AppColors.primaryBgColor,
                    //     textColor: AppColors.black,
                    //     border: Border.all(color: AppColors.primaryColor),
                    //   ),
                    // )
                  ],
                ),
              ],
            ),
          ),
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    10.verticalSpace,
                    TextWidget(
                      text: "${freelancerDetailModel?.firstName}'s Profile",
                      fontsize: 30,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                    10.verticalSpace,
                    TextWidget(
                      text: 'Get to know the freelancer before booking.',
                      fontsize: 16,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      textcolor: AppColors.grey4,
                      fontweight: FontWeight.w400,
                    ),
                    20.verticalSpace,
                    FreliancerListWidget(
                      onTap: () {},
                      margin: const EdgeInsets.only(right: 20),
                      padding: EdgeInsets.zero,
                      border: Border.all(color: AppColors.transparent),
                      height: 80,
                      onChatTap: () {
                        final otherUser = widget.freeliancerModel;

                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => ChatDetailScreen(
                              otherUserId: otherUser.id ?? 0,
                              otherUserName: '${otherUser.firstName ?? ''} ${otherUser.lastName ?? ''}',
                              otherUserProfile: otherUser.profileImage,
                            ),
                          ),
                        );
                      },
                      category: freelancerDetailModel?.makerCurrentlyStatus?.join(', ') ?? "Maker",
                      image: freelancerDetailModel?.profileImage ?? "",
                      name: '${freelancerDetailModel?.firstName} ${freelancerDetailModel?.lastName ?? ''}',
                      rating: rating,
                    ),
                    30.verticalSpace,
                    if (freelancerDetailModel?.bio != null) ...[
                      const TextWidget(
                        text: 'About',
                        fontsize: 20,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      10.verticalSpace,
                      TextWidget(
                        text: freelancerDetailModel?.bio ?? "",
                        fontsize: 16,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 10,
                        textcolor: AppColors.black,
                        fontweight: FontWeight.w400,
                      ),
                      20.verticalSpace,
                    ],
                    if (freelancerDetailModel?.skills?.isNotEmpty ?? false) ...[
                      const TextWidget(
                        text: 'My Skills',
                        fontsize: 20,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      10.verticalSpace,
                      ListView.separated(
                          separatorBuilder: (context, index) => const SizedBox(
                                height: 5,
                              ),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: freelancerDetailModel?.skills?.length ?? 0,
                          itemBuilder: (context, index) {
                            return Text('• ${freelancerDetailModel?.skills?[index].skillName} ');
                          }),
                      20.verticalSpace,
                    ],
                    if (selectedcategoryList.isNotEmpty && selectedcategoryList.length > 1) ...[
                      const TextWidget(
                        text: 'Portfolio',
                        fontsize: 20,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      10.verticalSpace,
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                            children: selectedcategoryList.map(
                          (e) {
                            return ValueListenableBuilder<Skills?>(
                                valueListenable: isSelectedSkill,
                                builder: (context, skill, _) {
                                  return InkWell(
                                    onTap: () {
                                      isSelectedSkill.value = e;
                                    },
                                    child: Container(
                                      margin: const EdgeInsets.only(left: 10),
                                      padding: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: AppColors.primaryColor,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Text(
                                        e.skillName ?? "",
                                        style: TextStyle(
                                          color: skill?.id == e.id ? AppColors.primaryColor : AppColors.black,
                                        ),
                                      ),
                                    ),
                                  );
                                });
                          },
                        ).toList()),
                      ),
                      20.verticalSpace,
                      ValueListenableBuilder<Skills?>(
                          valueListenable: isSelectedSkill,
                          builder: (context, skill, _) {
                            final files = skill?.files ?? [];
                            return StaggeredGrid.count(
                              crossAxisCount: getValueForScreenType(context: context, mobile: 4, tablet: 8),
                              mainAxisSpacing: 4,
                              crossAxisSpacing: 4,
                              children: files.mapIndexed(
                                (index, e) {
                                  return StaggeredGridTile.count(
                                    crossAxisCellCount: 2,
                                    mainAxisCellCount: 2,
                                    child: GestureDetector(
                                      onTap: () {
                                        log("${skill}skillssss");
                                        Navigator.push<Route<Widget>>(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => ViewFullImage(
                                              index: index,
                                              imageId: e.id,
                                              userId: e.userId,
                                              skills: skill,
                                              images: files.map((e) => e.filePath ?? "").toList(),
                                            ),
                                          ),
                                        );
                                      },
                                      child: Utility.imageLoader(
                                          url: e.filePath ?? '', placeholder: AppAssets.logo, fit: BoxFit.cover),
                                    ),
                                  );
                                },
                              ).toList(),
                            );
                          }),
                      20.verticalSpace,
                    ],
                    if (((reviewList.isNotEmpty))) ...[
                      Stack(
                        children: [
                          ListView.separated(
                            itemCount: reviewList.length,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            padding: const EdgeInsets.only(top: 5, bottom: 20),
                            itemBuilder: (context, index) {
                              return (reviewList.length - 1) == index
                                  ? VisibilityDetector(
                                      key: Key(index.toString()),
                                      onVisibilityChanged: (VisibilityInfo info) {
                                        if (!stop &&
                                            index == (reviewList.length - 1) &&
                                            !isLoading.value &&
                                            !isPageLoading.value) {
                                          getFreeliancerList(isFirst: false);
                                        }
                                      },
                                      child: Column(
                                        children: [
                                          reviewWidget(index),
                                          ValueListenableBuilder<bool>(
                                            valueListenable: isPageLoading,
                                            builder: (context, value, _) {
                                              if (isPageLoading.value) {
                                                return Utility.progress();
                                              }
                                              return const SizedBox();
                                            },
                                          )
                                        ],
                                      ),
                                    )
                                  : reviewWidget(index);
                            },
                            separatorBuilder: (context, index) {
                              return const SizedBox(
                                height: 5,
                              );
                            },
                          ),
                          if (!isLoading.value && reviewList.isEmpty) Utility.noDataWidget(text: 'No Reviews'),
                          ValueListenableBuilder(
                            valueListenable: isLoading,
                            builder: ((context, value, _) {
                              if (isLoading.value) {
                                return Utility.progress();
                              }
                              return const SizedBox();
                            }),
                          ),
                        ],
                      ),
                    ],
                    // const TextWidget(
                    //   text: 'Review',
                    //   fontsize: 20,
                    //   overflow: TextOverflow.ellipsis,
                    //   maxLines: 2,
                    // ),
                    // 10.verticalSpace,
                    // ListView.builder(
                    //     shrinkWrap: true,
                    //     physics: const NeverScrollableScrollPhysics(),
                    //     itemCount: 4,
                    //     itemBuilder: (context, index) {
                    //       return reviewWidget();
                    //     }),
                    // 20.verticalSpace,
                    CommonButtonWidget(
                      innerPaddding: const EdgeInsets.all(15),
                      padding: const EdgeInsets.all(10),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      border: Border.all(color: AppColors.primaryColor),
                      buttonColor: AppColors.primaryColor,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AvailabilityScreen(
                                  gender: widget.gender,
                                  freelancerDetailModel: freelancerDetailModel,
                                  isFormOnly: widget.isFormOnly,
                                  catergoryList: widget.catergoryList,
                                  userId: widget.freeliancerId,
                                  activeList: freelancerDetailModel?.activeLeaves,
                                  availableDays: freelancerDetailModel?.availableDays),
                            ));
                      },
                      text: 'Booking',
                    ),
                  ],
                ),
              ),
            ),
            ValueListenableBuilder(
              valueListenable: isLoading,
              builder: (BuildContext context, dynamic value, _) {
                if (isLoading.value) {
                  return Container(
                      color: AppColors.whiteColor,
                      height: MediaQuery.of(context).size.height,
                      child: Utility.progress());
                }
                return const SizedBox();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget reviewWidget(index) {
    return Container(
      padding: const EdgeInsets.all(15),
      margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 3),
      decoration: BoxDecoration(border: Border.all(color: AppColors.grey4), borderRadius: BorderRadius.circular(10)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FreliancerListWidget(
            onTap: () {},
            padding: EdgeInsets.zero,
            isForStar: true,
            border: Border.all(color: AppColors.transparent),
            image: reviewList[index].user?.profileImage ?? "",
            name: '${reviewList[index].user?.firstName ?? ""} ${reviewList[index].user?.lastName ?? ""}',
            rating: reviewList[index].rating.toString(),
            onChatTap: () {},
          ),
          10.verticalSpace,
          TextWidget(
            text: reviewList[index].comment ?? "",
            maxLines: 4,
          )
        ],
      ),
    );
  }

  Future<void> getFreeliancerList({required bool isFirst}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    if (await ApiManager.checkInternet()) {
      page += 1;
      var request = <String, dynamic>{};
      request['page'] = page.toString();
      request['per_page'] = perPage.toString();
      request['freelancer_id'] = widget.freeliancerId.toString();

      ReviewsListReponce response = ReviewsListReponce.fromJson(
        await ApiManager().getCall(AppString.reviewListUrl, request, context),
      );
      if (response.status == "1" && response.data != null && response.data!.isNotEmpty) {
        reviewList.addAll(response.data!);

        _notify();
      } else {
        noDataLogic(page);
      }
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
    } else {
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
      Utility.toast(message: 'No Internet Connection');
    }
  }

  getFreeLiancerDetail() async {
    log('in aPi');
    isLoading.value = true;
    if (await ApiManager.checkInternet()) {
      var request = <String, dynamic>{};

      FreelancerDetailResponce response = FreelancerDetailResponce.fromJson(
          await ApiManager().getCall(AppString.freeliancerDetailUrl(widget.freeliancerId), request, context));
      if (response.status == "1" && response.data != null) {
        freelancerDetailModel = response.data;
        rating = response.avgRating ?? '';
        final allSkill =
            freelancerDetailModel?.skills?.where((element) => element.files?.isNotEmpty ?? false).toList() ?? [];
        final files = allSkill.expand((element) => element.files ?? <Files>[]).toList();
        selectedcategoryList.clear();
        selectedcategoryList.add(Skills(skillName: 'All', files: files));
        selectedcategoryList.addAll(allSkill);
        isSelectedSkill.value = selectedcategoryList.first;
        isLoading.value = false;
        _notify();
      } else {
        isLoading.value = false;
        return Utility.toast(message: response.message);
      }
    } else {
      isLoading.value = false;
      return Utility.toast(message: 'No Internet Connection');
    }
  }

  Future<void> reportApi() async {
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;
      var request = <String, dynamic>{};
      request['user_id'] = freelancerDetailModel?.id.toString();
      request['description'] = reportController.text.trim();

      ReportResponse response =
          // ignore: use_build_context_synchronously
          ReportResponse.fromJson(await ApiManager().postCall(AppString.reportUrl, request, context));
      if (response.status == "1") {
        isLoading.value = false;
      }
      reportController.clear();
      isLoading.value = false;
      Utility.toast(message: response.message);
    } else {
      isLoading.value = false;
      Utility.toast(message: 'No Internet Connection');
    }
  }
}
