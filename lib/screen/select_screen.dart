import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../model/category_model.dart';
import '../model/user_model.dart';
import '../responce/category_responce.dart';
import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_widget.dart';
import 'freliancer_list_screen.dart';

class SelectServiceScreen extends StatefulWidget {
  const SelectServiceScreen({
    super.key,
    required this.type,
    required this.gender,
    required this.categoryId,
    required this.categoryName,
  });
  final String type;
  final String gender;
  final String categoryId;
  final String categoryName;

  @override
  State<SelectServiceScreen> createState() => _SelectServiceScreenState();
}

class _SelectServiceScreenState extends State<SelectServiceScreen> {
  final isSelectedServices = ValueNotifier<int?>(null);
  int page = 0;
  int perPage = 10;
  bool stop = false;
  List<CategoryModel> categoryList = [];
  List<CategoryModel> selectedcategoryList = [];
  late SharedPreferences sharedPreferences;
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isPageLoading = ValueNotifier<bool>(false);
  bool isChecked = false;
  UserModel? user;
  @override
  void initState() {
    super.initState();
    fetchCustomerData();
    getCategoryList(isFirst: true);
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  void noDataLogic(pageNumber) {
    page = pageNumber - 1;
    stop = true;
    _notify();
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(230.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 45,
                      width: 45,
                      child: Utility.imageLoader(
                        url: user?.profileImage ?? '',
                        isShapeCircular: true,
                        shape: BoxShape.circle,
                        placeholder: AppAssets.placeHolderImage,
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                Flexible(
                  child: TextWidget(
                    text: widget.categoryName,
                    fontsize: 22,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Select the Category.',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 20),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: categoryList.length,
                      itemBuilder: (context, index) {
                        return (categoryList.length - 1) == index
                            ? VisibilityDetector(
                                key: Key(index.toString()),
                                onVisibilityChanged: (VisibilityInfo info) {
                                  if (!stop &&
                                      index == (categoryList.length - 1) &&
                                      !isLoading.value &&
                                      !isPageLoading.value) {
                                    getCategoryList(isFirst: false);
                                  }
                                },
                                child: Column(
                                  children: [
                                    categoryListWidget(index),
                                    ValueListenableBuilder<bool>(
                                      valueListenable: isPageLoading,
                                      builder: (context, value, _) {
                                        if (isPageLoading.value) {
                                          return Center(child: Utility.progress());
                                        }
                                        return const SizedBox();
                                      },
                                    )
                                  ],
                                ),
                              )
                            : categoryListWidget(index);
                      },
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.025,
                    ),
                    CommonButtonWidget(
                      onTap: () {
                        final list = List<CategoryModel>.from(selectedcategoryList);
                        if (list.isNotEmpty) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => FreeLiancerScreen(
                                gender: widget.gender,
                                isFormOnly: (widget.type == 'REPAIR' || widget.type == 'ALTER') ? true : false,
                                catergoryList: list,
                              ),
                            ),
                          );
                          // selectedcategoryList.clear();
                          // _notify();
                        } else {
                          Utility.toast(message: 'Select one Category');
                        }
                      },
                      text: 'Done',
                    )
                  ],
                ),
                if (!isLoading.value && categoryList.isEmpty) Utility.noDataWidget(text: 'No Category'),
                ValueListenableBuilder(
                  valueListenable: isLoading,
                  builder: ((context, value, _) {
                    if (isLoading.value) {
                      return Utility.progress();
                    }
                    return const SizedBox();
                  }),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> getCategoryList({required bool isFirst}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    if (await ApiManager.checkInternet()) {
      page += 1;
      var request = <String, dynamic>{};
      request['page'] = page.toString();
      request['per_page'] = perPage.toString();
      request['type'] = widget.type;
      request['category_id'] = widget.categoryId;
      request['gender'] = widget.gender;
      CategoryResponce response = CategoryResponce.fromJson(
        await ApiManager().getCall(
          AppString.categoryListUrl,
          request,context
        ),
      );
      if (response.status == "1" && response.data != null && response.data!.isNotEmpty) {
        categoryList.addAll(response.data!);

        _notify();
      } else {
        noDataLogic(page);
      }
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
    } else {
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
      Utility.toast(message: 'No Internet Connection');
    }
  }

  Widget categoryListWidget(index) {
    return ListTile(
      title: Text(categoryList[index].name ?? ""),
      leading: Checkbox(
        value: selectedcategoryList.contains(categoryList[index]),
        onChanged: (value) {
          if (selectedcategoryList.contains(categoryList[index])) {
            selectedcategoryList.remove(categoryList[index]);
          } else {
            selectedcategoryList.add(categoryList[index]);
          }
          _notify();
        },
      ),

      // 'assets/image/ruler.png',
    );
  }
}
