import 'package:flutter/material.dart';
import 'package:trendi_people/screen/home.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_widget.dart';

class BookingConformSCreen extends StatefulWidget {
  const BookingConformSCreen({super.key});

  @override
  State<BookingConformSCreen> createState() => _BookingConformSCreenState();
}

class _BookingConformSCreenState extends State<BookingConformSCreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        Container(
          decoration: const BoxDecoration(
            color: AppColors.whiteColor,
            image: DecorationImage(fit: BoxFit.cover, image: AssetImage(AppAssets.onBoarding2)),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 25, right: 25, bottom: 50),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const TextWidget(
                text: 'Your booking has been confirmed',
                textAlign: TextAlign.center,
                textcolor: AppColors.whiteColor,
                maxLines: 2,
                fontsize: 30,
              ),
              20.verticalSpace,
              const TextWidget(
                text: 'The freelancer will receive your booking soon.',
                textcolor: AppColors.whiteColor,
                maxLines: 3,
                fontweight: FontWeight.w400,
                textAlign: TextAlign.center,
                letterSpacing: 0.5,
              ),
              25.verticalSpace,
              CommonButtonWidget(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  border: Border.all(color: AppColors.primaryColor),
                  buttonColor: AppColors.primaryColor,
                  onTap: () {
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const Home(initialIndex: 1),
                      ),
                      (Route<dynamic> route) => false,
                    );
                  },
                  text: 'Check your bookings here!'),
              10.verticalSpace,
              CommonButtonWidget(
                image: AppAssets.homeIcon,
                fontSize: 16,
                textColor: AppColors.primaryColor,
                fontWeight: FontWeight.w600,
                border: Border.all(color: AppColors.primaryColor),
                buttonColor: AppColors.transparent,
                onTap: () {
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const Home(),
                    ),
                    (Route<dynamic> route) => false,
                  );
                },
                text: 'Back Home',
              ),
            ],
          ),
        ),
      ],
    ));
  }
}
