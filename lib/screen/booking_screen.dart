import 'package:flutter/material.dart';
import 'package:trendi_people/model/order_history_model.dart';
import 'package:trendi_people/model/user_model.dart';
import 'package:trendi_people/responce/order_history_responce.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/text_widget.dart';
import 'order_details_screen.dart';

class BookingScreen extends StatefulWidget {
  const BookingScreen({super.key, this.initialIndex});
   final int? initialIndex;

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  int page = 0;
  int perPage = 10;
  bool stop = false;
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isPageLoading = ValueNotifier<bool>(false);
  List<OrderHistoryModel> orderHistory = [];
  UserModel? user;

  @override
  void initState() {
    super.initState();
    fetchCustomerData();

    getOrderList(isFirst: true);
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();

    _notify();
  }

  void noDataLogic(pageNumber) {
    page = pageNumber - 1;
    stop = true;
    _notify();
  }

  _refresh() {
    orderHistory.clear();
    page = 0;
    stop = false;
    _notify();
    getOrderList(isFirst: true);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        // appBar: PreferredSize(
        //   preferredSize: const Size.fromHeight(130.0),
        //   child: Padding(
        //     padding:
        //         //  const EdgeInsets.all(0),
        //         const EdgeInsets.only(
        //       left: 25,
        //       right: 25,
        //       top: 20,
        //     ),
        //     child: Column(
        //       mainAxisAlignment: MainAxisAlignment.start,
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: [
        //         30.verticalSpace,
        //         const TextWidget(
        //           text: 'Your Bookings',
        //           fontsize: 30,
        //           overflow: TextOverflow.ellipsis,
        //           maxLines: 2,
        //         ),
        //         10.verticalSpace,
        //         TextWidget(
        //           text: 'The list of your current booking list',
        //           fontsize: 16,
        //           overflow: TextOverflow.ellipsis,
        //           maxLines: 2,
        //           textcolor: AppColors.grey4,
        //           fontweight: FontWeight.w400,
        //         ),
        //       ],
        //     ),
        //   ),
        // ),
        body: RefreshIndicator(
          color: AppColors.primaryColor,
          onRefresh: () async {
            _refresh();
          },
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 25, right: 25, top: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextWidget(
                      text: 'Your Bookings',
                      fontsize: 30,
                      overflow: TextOverflow.ellipsis,
                      textcolor: AppColors.black,
                      maxLines: 2,
                    ),
                    10.verticalSpace,
                    TextWidget(
                      text: 'This is a list of your current bookings',
                      fontsize: 16,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      textcolor: AppColors.grey4,
                      fontweight: FontWeight.w400,
                    ),
                    30.verticalSpace,
                    Flexible(
                      child: ListView.separated(
                        separatorBuilder: (context, index) => Container(
                          height: 10,
                        ),
                        itemCount: orderHistory.length,
                        itemBuilder: (context, index) {
                          return (orderHistory.length - 1) == index
                              ? VisibilityDetector(
                                  key: Key(index.toString()),
                                  onVisibilityChanged: (VisibilityInfo info) {
                                    if (!stop &&
                                        index == (orderHistory.length - 1) &&
                                        !isLoading.value &&
                                        !isPageLoading.value) {
                                      getOrderList(isFirst: false);
                                    }
                                  },
                                  child: Column(
                                    children: [
                                      orderConatiner(index),
                                      ValueListenableBuilder<bool>(
                                        valueListenable: isPageLoading,
                                        builder: (context, value, _) {
                                          if (value) {
                                            return Center(child: Utility.progress());
                                          }
                                          return const SizedBox();
                                        },
                                      )
                                    ],
                                  ),
                                )
                              : orderConatiner(index);
                        },
                      ),
                    ),
                    20.verticalSpace,
                    TextWidget(
                        text: 'End of the list.',
                        fontsize: 13,
                        textcolor: AppColors.grey5,
                        fontweight: FontWeight.w400),
                  ],
                ),
              ),
              if (!isLoading.value && orderHistory.isEmpty) Utility.noDataWidget(text: 'No Item Found'),
              ValueListenableBuilder(
                valueListenable: isLoading,
                builder: ((context, value, _) {
                  if (isLoading.value) {
                    return Utility.progress();
                  }
                  return const SizedBox();
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget orderConatiner(index) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OrderDetailsScreen(id: orderHistory[index].id ?? 0),
            ));
      },
      child: Container(
          // margin: const EdgeInsets.symmetric(horizontal: 10),
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          decoration: BoxDecoration(
              borderRadius: index == 0
                  ? const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                      bottomLeft: Radius.circular(5),
                      bottomRight: Radius.circular(5))
                  : index == (orderHistory.length - 1)
                      ? const BorderRadius.only(
                          bottomLeft: Radius.circular(20),
                          bottomRight: Radius.circular(20),
                          topLeft: Radius.circular(5),
                          topRight: Radius.circular(5))
                      : BorderRadius.circular(5),
              border: Border.all(color: AppColors.grey4),
              color: AppColors.white24),
          child: Row(
            children: [
              Container(
                margin: const EdgeInsets.only(right: 10),
                height: 60,
                width: 60,
                child: Utility.imageLoader(
                  url: orderHistory[index].freelancer?.profileImage ?? '',
                  isShapeCircular: true,
                  shape: BoxShape.circle,
                  placeholder: AppAssets.placeHolderImage,
                ),
              ),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                        text:
                            '${orderHistory[index].mainCategory?.name ?? ""} ${orderHistory[index].categories?.first.name ?? ""}',
                        fontsize: 20,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textcolor: AppColors.blackColor,
                        fontweight: FontWeight.bold),
                    7.verticalSpace,
                    TextWidget(
                        text: orderHistory[index].freelancer?.firstName ?? "",
                        fontsize: 15,
                        textcolor: AppColors.blackColor,
                        fontweight: FontWeight.w400),
                    7.verticalSpace,
                    TextWidget(
                        text: orderHistory[index].startDate ?? "",
                        fontsize: 15,
                        textcolor: AppColors.primaryColor,
                        fontweight: FontWeight.w400),
                  ],
                ),
              )
            ],
          )),
    );
  }

  Future<void> getOrderList({required bool isFirst}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    if (await ApiManager.checkInternet()) {
      page += 1;
      var request = <String, dynamic>{};
      request['page'] = page.toString();
      request['per_page'] = perPage.toString();
      OrderHistoryResponce response = OrderHistoryResponce.fromJson(
        await ApiManager().getCall(AppString.orderHistoryUrl, request, context),
      );
      if (response.status == "1" && response.data != null && response.data!.isNotEmpty) {
        orderHistory.addAll(response.data!);

        _notify();
      } else {
        noDataLogic(page);
      }
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
    } else {
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
      Utility.toast(message: 'No Internet Connection');
    }
  }
}
