import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:trendi_people/screen/create_service_scnree.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../model/user_model.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/utility.dart';
import '../widget/text_widget.dart';

class FrelincerServiceScreen extends StatefulWidget {
  const FrelincerServiceScreen({super.key});

  @override
  State<FrelincerServiceScreen> createState() => _FrelincerServiceScreenState();
}

class _FrelincerServiceScreenState extends State<FrelincerServiceScreen> {
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  final ValueNotifier<String> _character = ValueNotifier<String>('');
  String title = '';
  UserModel? user;
  @override
  void initState() {
    super.initState();

    fetchCustomerData();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Padding(
          padding:
              //  const EdgeInsets.all(0),
              const EdgeInsets.only(left: 25, right: 25, top: 20),
          child: Column(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            text: 'Hi ${user?.firstName ?? ""}',
                            fontsize: 30,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                          10.verticalSpace,
                          TextWidget(
                            text: 'What can we do for you today?',
                            fontsize: 16,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            textcolor: AppColors.grey4,
                            fontweight: FontWeight.w400,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 45,
                        width: 45,
                        child: Utility.imageLoader(
                          url: user?.profileImage ?? '',
                          isShapeCircular: true,
                          shape: BoxShape.circle,
                          placeholder: AppAssets.placeHolderImage,
                        ),
                      ),
                    ],
                  ),
                  30.verticalSpace,
                  const TextWidget(
                    text: 'Service',
                    fontsize: 30,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                  30.verticalSpace,
                  ValueListenableBuilder(
                      valueListenable: _character,
                      builder: (context, value, _) {
                        return Column(
                          children: [
                            rowIcon(
                                text: 'Tailor Me/Create',
                                onTap: () {
                                  _character.value = 'MAKE';
                                  title = 'Tailor';
                                  log("tailor");
                                }),
                            10.verticalSpace,
                            rowIcon(
                                text: 'Repair',
                                onTap: () {
                                  _character.value = 'REPAIR';
                                  title = 'Repair';
                                  log("repair");
                                }),
                            10.verticalSpace,
                            rowIcon(
                                text: 'Alter',
                                onTap: () {
                                  _character.value = 'ALTER';
                                  title = 'Alter';
                                }),
                            10.verticalSpace,
                            rowIcon(
                                text: 'Style me up',
                                onTap: () {
                                  _character.value = 'STYLE_ME_UP';
                                  title = 'Style me up';
                                }),
                          ],
                        );
                      }),
                ],
              ),
              30.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }

  Widget rowIcon({String? text, VoidCallback? onTap}) {
    return Theme(
      data:
          ThemeData(dividerColor: AppColors.transparent, iconTheme: const IconThemeData(color: AppColors.primaryColor)),
      child: Container(
        decoration: BoxDecoration(
            // color: Color(0xfff4f7fa),
            border: Border.all(color: AppColors.grey4),
            borderRadius: const BorderRadius.all(
              Radius.circular(10),
            )),
        child: ExpansionTile(
          iconColor: AppColors.primaryColor,
          onExpansionChanged: (value) {},
          childrenPadding: const EdgeInsets.all(10),
          title: TextWidget(
            text: text ?? "",
            fontweight: FontWeight.bold,
            fontsize: 16,
          ),
          expandedCrossAxisAlignment: CrossAxisAlignment.start,
          expandedAlignment: Alignment.centerLeft,
          children: [
            GestureDetector(
              onTap: () {
                onTap?.call();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CreateRequestScreen(
                      gender: 'men',
                      type: _character.value,
                      title: title,
                    ),
                  ),
                );
              },
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.all(10),
                decoration: const BoxDecoration(
                    color: Color(0xfff4f7fa),
                    // border: Border.all(color: AppColors.primaryColor),
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10))),
                child: const TextWidget(
                  text: 'Men’s',
                  fontweight: FontWeight.bold,
                  fontsize: 16,
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                onTap?.call();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CreateRequestScreen(
                      gender: 'women',
                      type: _character.value,
                      title: title,
                    ),
                  ),
                );
              },
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 10),
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: const Color(0xff2B609E).withOpacity(0.05),
                    // border: Border.all(color: AppColors.primaryColor),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(3),
                    )),
                child: const TextWidget(
                  text: 'Women’s',
                  fontweight: FontWeight.bold,
                  fontsize: 16,
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                onTap?.call();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CreateRequestScreen(
                      gender: 'other',
                      type: _character.value,
                      title: title,
                    ),
                  ),
                );
              },
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.all(10),
                decoration: const BoxDecoration(
                    color: Color(0xfff4f7fa),
                    // border: Border.all(color: AppColors.primaryColor),
                    borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10))),
                child: const TextWidget(
                  text: 'Others',
                  fontweight: FontWeight.bold,
                  fontsize: 16,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
