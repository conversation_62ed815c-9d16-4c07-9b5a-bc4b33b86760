import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:trendi_people/model/chat_user_parent_model.dart';
import 'package:trendi_people/model/user_model.dart';
import 'package:trendi_people/responce/chat_user_list_response.dart';
import 'package:trendi_people/screen/chat/chat_detail_screen.dart';
import 'package:trendi_people/utility/api_manager.dart';
import 'package:trendi_people/utility/app_string.dart';
import 'package:trendi_people/utility/firebase_chat_service.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/utility/utility.dart';
import 'package:trendi_people/widget/chat_user_widget.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../utility/app_colors.dart';
import '../widget/text_widget.dart';

class ChatRoomScreen extends StatefulWidget {
  const ChatRoomScreen({super.key});

  @override
  State<ChatRoomScreen> createState() => _ChatRoomScreenState();
}

class _ChatRoomScreenState extends State<ChatRoomScreen> {
  final isLoading = ValueNotifier<bool>(false);
  final isLoadingPage = ValueNotifier<bool>(false);
  int page = 0;
  bool stop = false;
  final userList = ValueNotifier<List<UserModel>>([]);
  final currentUser = ValueNotifier<UserModel?>(null);
  @override
  void initState() {
    getUser();
    super.initState();
  }

  void getUser() async {
    if (await ApiManager.checkInternet()) {
      currentUser.value = await Utility.getUser();
      if (page == 0) {
        isLoading.value = true;
      } else {
        isLoadingPage.value = true;
      }
      page = page + 1;
      var request = <String, dynamic>{};
      request['page'] = page.toString();

      ChatUserListResponse response =
          ChatUserListResponse.fromJson(await ApiManager().getCall(AppString.chatList, request, context));
      if (response.status == "1" && response.data != null && response.data!.isNotEmpty) {
        userList.value = [...userList.value, ...response.data!];
      } else {
        noDatalogic();
      }

      isLoading.value = false;
      isLoadingPage.value = false;
    } else {
      Utility.toast(message: 'No Internet');
    }
  }

  void noDatalogic() {
    page = page - 1;
    stop = true;
  }

  _refresh() {
    userList.value = [];
    page = 0;
    stop = false;

    getUser();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 25, right: 25, top: 0, bottom: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                20.verticalSpace,
                const TextWidget(
                  text: 'Chat Room',
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Your chat list with other user',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
                30.verticalSpace,
                ValueListenableBuilder<List<UserModel>>(
                  valueListenable: userList,
                  builder: (context, list, _) {
                    return Flexible(
                      child: RefreshIndicator(
                        onRefresh: () async {
                          _refresh();
                        },
                        child: ListView.separated(
                          separatorBuilder: (context, index) => Container(height: 5),
                          itemCount: list.length,
                          itemBuilder: (context, index) {
                            return (list.length - 1) == index
                                ? VisibilityDetector(
                                    key: Key(index.toString()),
                                    onVisibilityChanged: (VisibilityInfo info) {
                                      if (!stop &&
                                          index == (list.length - 1) &&
                                          !isLoading.value &&
                                          !isLoadingPage.value) {
                                        getUser();
                                      }
                                    },
                                    child: Column(
                                      children: [
                                        itemView(list[index]),
                                        ValueListenableBuilder<bool>(
                                          valueListenable: isLoadingPage,
                                          builder: (context, value, _) {
                                            if (value) {
                                              return Center(child: Utility.progress());
                                            }
                                            return const SizedBox();
                                          },
                                        )
                                      ],
                                    ),
                                  )
                                : itemView(list[index]);
                          },
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          ValueListenableBuilder<bool>(
            valueListenable: isLoading,
            builder: (context, loading, _) {
              if (loading) {
                return Utility.progress();
              }
              return const SizedBox();
            },
          ),
          ValueListenableBuilder<List<UserModel>>(
              valueListenable: userList,
              builder: (context, list, _) {
                return ValueListenableBuilder<bool>(
                    valueListenable: isLoadingPage,
                    builder: (context, loadingPage, _) {
                      return ValueListenableBuilder<bool>(
                          valueListenable: isLoading,
                          builder: (context, loading, _) {
                            if (!loading && !loadingPage && list.isEmpty) {
                              return Center(
                                child: Text(
                                  'No Chat',
                                  style: TextStyle(color: AppColors.black, fontWeight: FontWeight.bold),
                                ),
                              );
                            }
                            return const SizedBox();
                          });
                    });
              }),
        ],
      ),
    );
  }

  Widget itemView(UserModel userModel) {
    final time = DateTime.tryParse(userModel.pivot?.updatedAt ?? '');
    ChatUserParentModel? chatUser;
    return StreamBuilder<QuerySnapshot>(
        stream: FireaseChatService.getUsersChatListIndividualBadgeValue(currentUser.value?.id ?? 0, userModel.id ?? 0),
        builder: (_, snapshot) {
          if (snapshot.data != null) {
            for (var document in snapshot.data!.docs) {
              chatUser = ChatUserParentModel.fromJson(document.data()! as Map<String, dynamic>);
            }
          }
          return ChatUserWidget(
            imageUrl: userModel.profileImage,
            name: '${userModel.firstName ?? ''} ${userModel.lastName != null ? userModel.lastName ?? '' : ''}',
            number: (chatUser == null || chatUser?.userBbadgeValue == 0) ? null : '${chatUser?.userBbadgeValue}',
            onTap: () async {
              var refresh = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => ChatDetailScreen(
                    otherUserId: userModel.id ?? 0,
                    otherUserName:
                        '${userModel.firstName ?? ''} ${userModel.lastName != null ? userModel.lastName ?? '' : ''}',
                    otherUserProfile: userModel.profileImage,
                  ),
                ),
              );
              if (refresh != null) {
                _refresh();
              }
            },
            time: chatUser?.formmetedTime ?? '',
            category: chatUser?.type ?? '',
          );
        });
  }
}
