import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../utility/app_colors.dart';

class ContactUsScreen extends StatefulWidget {
  const ContactUsScreen({super.key});

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          appBar: AppBar(
            backgroundColor: AppColors.primaryColor,
            centerTitle: true,
            title: const Text('Contact Us', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          ),
          body: InAppWebView(
              onLoadStart: (controller, url) async {
                setState(() {});
              },
              initialUrlRequest: URLRequest(
                url: Uri.parse('https://trendipeople.com/contact-us/'),
              ))),
    );
  }
}
