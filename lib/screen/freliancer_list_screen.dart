import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:trendi_people/model/category_model.dart';
import 'package:trendi_people/model/freeliancer_model.dart';
import 'package:trendi_people/responce/freeliancer_list_responce.dart';
import 'package:trendi_people/screen/chat/chat_detail_screen.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../model/user_model.dart';
import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/freliancer_list_widget.dart';
import '../widget/text_widget.dart';
import 'freeliancer_detail_sceen.dart';

class FreeLiancerScreen extends StatefulWidget {
  const FreeLiancerScreen({super.key, this.catergoryList, required this.isFormOnly, this.gender});
  final List<CategoryModel>? catergoryList;
  final bool isFormOnly;
  final String? gender;

  @override
  State<FreeLiancerScreen> createState() => _FreeLiancerScreenState();
}

class _FreeLiancerScreenState extends State<FreeLiancerScreen> {
  int page = 0;
  int perPage = 10;
  bool stop = false;
  List<FreeliancerModel> freeLiancerList = [];
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isPageLoading = ValueNotifier<bool>(false);
  UserModel? user;

  @override
  void initState() {
    super.initState();
    getFreeliancerList(isFirst: true);
    log(widget.isFormOnly.toString());
    fetchCustomerData();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  void noDataLogic(pageNumber) {
    page = pageNumber - 1;
    stop = true;
    _notify();
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(190.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 45,
                      width: 45,
                      child: Utility.imageLoader(
                        url: user?.profileImage ?? '',
                        isShapeCircular: true,
                        shape: BoxShape.circle,
                        placeholder: AppAssets.placeHolderImage,
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                const TextWidget(
                  text: 'Maker list',
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Select the Maker you want below.',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 20),
          child: Stack(
            children: [
              ListView.separated(
                itemCount: freeLiancerList.length,
                padding: const EdgeInsets.only(top: 5, bottom: 5),
                itemBuilder: (context, index) {
                  return (freeLiancerList.length - 1) == index
                      ? VisibilityDetector(
                          key: Key(index.toString()),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (!stop &&
                                index == (freeLiancerList.length - 1) &&
                                !isLoading.value &&
                                !isPageLoading.value) {
                              getFreeliancerList(isFirst: false);
                            }
                          },
                          child: Column(
                            children: [
                              freelancerListWidget(index),
                              ValueListenableBuilder<bool>(
                                valueListenable: isPageLoading,
                                builder: (context, value, _) {
                                  if (isPageLoading.value) {
                                    return Utility.progress();
                                  }
                                  return const SizedBox();
                                },
                              )
                            ],
                          ),
                        )
                      : freelancerListWidget(index);
                },
                separatorBuilder: (context, index) {
                  return const SizedBox(
                    height: 5,
                  );
                },
              ),
              if (!isLoading.value && freeLiancerList.isEmpty) Utility.noDataWidget(text: 'Oops No Maker Found'),
              ValueListenableBuilder(
                valueListenable: isLoading,
                builder: ((context, value, _) {
                  if (isLoading.value) {
                    return Utility.progress();
                  }
                  return const SizedBox();
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> getFreeliancerList({required bool isFirst}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    if (await ApiManager.checkInternet()) {
      page += 1;
      var request = <String, dynamic>{};
      request['page'] = page.toString();
      request['per_page'] = perPage.toString();
      request['gender'] = widget.gender;
      if (widget.catergoryList!.isNotEmpty) {
        for (int i = 0; i < widget.catergoryList!.length; i++) {
          request['categories[$i]'] = widget.catergoryList![i].id.toString();
        }
      }

      FreelancersListResponce response = FreelancersListResponce.fromJson(
        await ApiManager().getCall(AppString.freeliancerListUrl, request, context),
      );
      if (response.status == "1" && response.data != null && response.data!.isNotEmpty) {
        freeLiancerList.addAll(response.data!);

        _notify();
      } else {
        noDataLogic(page);
      }
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
    } else {
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
      Utility.toast(message: 'No Internet Connection');
    }
  }

  Widget freelancerListWidget(index) {
    return FreliancerListWidget(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FreeliancerDetailScreen(
                freeliancerModel: freeLiancerList[index],
                gender: widget.gender,
                isFormOnly: widget.isFormOnly,
                freeliancerId: freeLiancerList[index].id ?? 0,
                catergoryList: widget.catergoryList),
          ),
        );
      },
      onChatTap: () {
        final otherUser = freeLiancerList[index];

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => ChatDetailScreen(
              otherUserId: otherUser.id ?? 0,
              otherUserName: '${otherUser.firstName ?? ''} ${otherUser.lastName ?? ''}',
              otherUserProfile: otherUser.profileImage,
            ),
          ),
        );
      },
      category: freeLiancerList[index].makerCurrentlyStatus?.join(', ') ?? "Maker",
      image: freeLiancerList[index].profileImage ?? "",
      name: '${freeLiancerList[index].firstName ?? ""} ${freeLiancerList[index].lastName ?? ""}',
      rating: freeLiancerList[index].averageRating ?? '0.0',
    );
  }
}
