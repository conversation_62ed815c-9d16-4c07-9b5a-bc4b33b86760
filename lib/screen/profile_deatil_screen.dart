import 'package:flutter/material.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../model/user_model.dart';
import '../utility/app_colors.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_field_widget.dart';
import '../widget/text_widget.dart';
import 'my_profile_screen.dart';

class ProfileDetailScreen extends StatefulWidget {
  const ProfileDetailScreen({super.key});

  @override
  State<ProfileDetailScreen> createState() => _ProfileDetailScreenState();
}

class _ProfileDetailScreenState extends State<ProfileDetailScreen> {
  UserModel? user;
  @override
  void initState() {
    super.initState();

    fetchCustomerData();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final bioController = TextEditingController();
  final fullNameController = TextEditingController();

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    fullNameController.text = '${user?.firstName ?? ""} ${user?.lastName ?? ""}';

    emailController.text = user?.email ?? "";
    phoneController.text = user?.phoneNumber ?? "";
    bioController.text = user?.bio ?? "";

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.only(left: 25, right: 25, top: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(
              text: 'My Profile',
              fontsize: 30,
              overflow: TextOverflow.ellipsis,
              textcolor: AppColors.black,
              maxLines: 2,
            ),
            10.verticalSpace,
            TextWidget(
              text: 'Edit my profile & settings',
              fontsize: 16,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              textcolor: AppColors.grey4,
              fontweight: FontWeight.w400,
            ),
            30.verticalSpace,
            SizedBox(
              height: 100,
              width: 100,
              child: Utility.imageLoader(
                  url: user?.profileImage ?? "",
                  placeholder: AppAssets.placeHolderImage,
                  isShapeCircular: true,
                  shape: BoxShape.circle),
            ),
            20.verticalSpace,
            AppTextField(
              borderColor: AppColors.primaryColor,
              inputTextStyle: TextStyle(color: AppColors.grey5),
              controller: fullNameController,
              readOnly: true,
              preffixIcon: Padding(
                padding: const EdgeInsets.only(left: 25, right: 20),
                child: Image.asset(
                  AppAssets.profileIcon,
                  color: AppColors.blackColor,
                  height: 24,
                  width: 24,
                ),
              ),
            ),
            20.verticalSpace,
            AppTextField(
              borderColor: AppColors.primaryColor,
              inputTextStyle: TextStyle(color: AppColors.grey5),
              controller: emailController,
              readOnly: true,
              preffixIcon: Padding(
                padding: const EdgeInsets.only(left: 25, right: 20),
                child: Image.asset(
                  AppAssets.mailIcon,
                  color: AppColors.blackColor,
                  height: 24,
                  width: 24,
                ),
              ),
            ),
            20.verticalSpace,
            AppTextField(
              borderColor: AppColors.primaryColor,
              inputTextStyle: TextStyle(color: AppColors.grey5),
              controller: phoneController,
              readOnly: true,
              preffixIcon: Padding(
                padding: const EdgeInsets.only(left: 25, right: 20),
                child: Image.asset(
                  AppAssets.phoneIcon,
                  color: AppColors.blackColor,
                  height: 24,
                  width: 24,
                ),
              ),
            ),
            20.verticalSpace,
            AppTextField(
              borderColor: AppColors.primaryColor,
              inputTextStyle: TextStyle(color: AppColors.grey5),
              controller: bioController,
              readOnly: true,
              preffixIcon: Padding(
                padding: const EdgeInsets.only(left: 25, right: 20),
                child: Image.asset(
                  AppAssets.bioIcon,
                  color: AppColors.blackColor,
                  height: 24,
                  width: 24,
                ),
              ),
            ),
            20.verticalSpace,
            CommonButtonWidget(
              innerPaddding: const EdgeInsets.all(15),
              // padding: const EdgeInsets.all(10),
              fontSize: 16,
              textColor: AppColors.black,
              fontWeight: FontWeight.w600,
              border: Border.all(color: AppColors.primaryColor),
              buttonColor: AppColors.primaryBgColor,
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MyProfileScreen(),
                    ));
              },
              text: 'Edit my Settings & Profile',
            ),
            20.verticalSpace,
          ],
        ),
      ),
    );
  }
}
