import 'dart:async';

import 'package:flutter/material.dart';
import 'package:trendi_people/screen/home.dart';
import 'package:trendi_people/screen/on_boarding_screen.dart';

import '../../utility/app_assets.dart';
import '../model/user_model.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    verify();
  }

  void verify() async {
    final isVerified = await varifyUser();
    Timer(
      const Duration(seconds: 3),
      (() {
        if (isVerified) {
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home()));
        } else {
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const OnBordingHelloScreen()));
        }
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.whiteColor,
      child: Image.asset(
        AppAssets.splashScreen,
      ),
    );
  }

  Future<bool> varifyUser() async {
    String? token = await Utility.getPref(key: AppString.tokenKey);
    UserModel? userModel = await Utility.getUser();

    if (token != null && userModel != null) {
      return true;
    }
    return false;
  }
}
