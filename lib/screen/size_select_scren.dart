import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/gender_container_widget.dart';

import '../model/user_model.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_widget.dart';

class SizeSelectScreen extends StatefulWidget {
  const SizeSelectScreen({super.key});

  @override
  State<SizeSelectScreen> createState() => _SizeSelectScreenState();
}

class _SizeSelectScreenState extends State<SizeSelectScreen> {
  UserModel? user;
  @override
  void initState() {
    super.initState();

    fetchCustomerData();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(190.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 45,
                      width: 45,
                      child: Utility.imageLoader(
                        url: user?.profileImage ?? '',
                        isShapeCircular: true,
                        shape: BoxShape.circle,
                        placeholder: AppAssets.placeHolderImage,
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                const TextWidget(
                  text: 'Size (cm)',
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Please fill all the informations below',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TextWidget(
                  text: 'What are your size measurements?',
                  fontsize: 25,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                const GenderContainerWidget(
                  name: '28',
                  padding: EdgeInsets.all(20),
                  margin: EdgeInsets.only(top: 8),
                ),
                const GenderContainerWidget(
                  name: 'Waist',
                  padding: EdgeInsets.all(20),
                  margin: EdgeInsets.only(top: 8),
                ),
                const GenderContainerWidget(
                  name: 'Thigh',
                  margin: EdgeInsets.only(top: 8),
                  padding: EdgeInsets.all(20),
                ),
                const GenderContainerWidget(
                  margin: EdgeInsets.only(top: 8),
                  name: '16 inch',
                  padding: EdgeInsets.all(20),
                ),
                const GenderContainerWidget(
                  margin: EdgeInsets.only(top: 8),
                  name: '16 inch',
                  padding: EdgeInsets.all(20),
                ),
                // GridView.builder(
                //   shrinkWrap: true,
                //   itemCount: 6,
                //   physics: const ScrollPhysics(),
                //   gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                //     crossAxisCount: 3,
                //     crossAxisSpacing: 8,
                //     mainAxisSpacing: 8,
                //     childAspectRatio: 0.75,
                //   ),
                //   itemBuilder: (context, index) {
                //     return ValueListenableBuilder(
                //       valueListenable: isSelectedServices,
                //       builder: (context, value, _) {
                //         return ServiceCategoriesWidget(
                //           title: 'Tailor',
                //           image: 'assets/image/ruler.png',
                //           background: (value == index) ? AppColors.primaryBgColor : AppColors.whiteColor,
                //           borderColor: (value == index) ? AppColors.primaryColor : AppColors.black,
                //           onTap: () {
                //             log("message");
                //             isSelectedServices.value = index;
                //           },
                //         );
                //       },
                //     );
                //   },
                // ),
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.025,
                ),
                const CommonButtonWidget(
                  // onTap: () {
                  //   Navigator.push(
                  //     context,
                  //     MaterialPageRoute(
                  //       builder: (context) => const FreeLiancerScreen(),
                  //     ),
                  //   );
                  // },
                  text: 'Done',
                ),
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.025,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
