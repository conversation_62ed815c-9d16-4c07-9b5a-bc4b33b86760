import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:trendi_people/responce/comman_response.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_field_widget.dart';
import '../widget/text_widget.dart';

class FeedBackScreen extends StatefulWidget {
  const FeedBackScreen({super.key, required this.orderId, required this.freelancerId});
  final String orderId;
  final String freelancerId;

  @override
  State<FeedBackScreen> createState() => _FeedBackScreenState();
}

class _FeedBackScreenState extends State<FeedBackScreen> {
  final ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  final feedBackController = TextEditingController();
  double overrallRatingValue = 0;
  double communicationRatingValue = 0;
  double qualityRatingValue = 0;
  double valueForMoneyValue = 0;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(175.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(
              left: 25,
              right: 25,
              top: 20,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade100),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.arrow_back,
                      color: AppColors.black,
                    ),
                  ),
                ),
                10.verticalSpace,
                const TextWidget(
                  text: 'Give Feedback',
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                10.verticalSpace,
                TextWidget(
                  text: 'Your Profile and Setting ',
                  fontsize: 16,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textcolor: AppColors.grey4,
                  fontweight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(
                  left: 25,
                  right: 25,
                  top: 20,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const TextWidget(
                      text: "How did we do?",
                      maxLines: 3,
                      fontweight: FontWeight.bold,
                      fontsize: 20,
                    ),
                    5.verticalSpace,
                    RatingBar(
                      initialRating: 0.0,
                      direction: Axis.horizontal,
                      itemSize: 30,
                      allowHalfRating: true,
                      itemCount: 5,
                      ratingWidget: RatingWidget(
                        full: Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(color: AppColors.primaryColor, shape: BoxShape.circle),
                            child: Image.asset(
                              // fit: BoxFit.cover,
                              AppAssets.ratingWhite,
                            )),
                       half: Container(
                            height: 75,
                            width: 75,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: Image.asset(
                              AppAssets.halfIcon,
                              fit: BoxFit.contain,
                            )),
                        empty: Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: BoxDecoration(color: AppColors.grey3, shape: BoxShape.circle),
                            child: Image.asset(
                              AppAssets.ratingBlack,
                              height: 10,
                            )),
                      ),
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      onRatingUpdate: (rating) {
                        overrallRatingValue = (rating);
                        print(rating);
                      },
                    ),
                    15.verticalSpace,
                    const TextWidget(
                      text: "Communication: How responsive and accessible was this TRENDiPERSON?",
                      maxLines: 3,
                      fontweight: FontWeight.bold,
                      fontsize: 20,
                    ),
                    5.verticalSpace,
                    RatingBar(
                      initialRating: 0.0,
                      direction: Axis.horizontal,
                      itemSize: 30,
                      allowHalfRating: true,
                      itemCount: 5,
                      ratingWidget: RatingWidget(
                        full: Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(color: AppColors.primaryColor, shape: BoxShape.circle),
                            child: Image.asset(
                              // fit: BoxFit.cover,
                              AppAssets.ratingWhite,
                            )),
                       half: Container(
                            height: 75,
                            width: 75,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: Image.asset(
                              AppAssets.halfIcon,
                              fit: BoxFit.contain,
                            )),
                        empty: Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: BoxDecoration(color: AppColors.grey3, shape: BoxShape.circle),
                            child: Image.asset(
                              AppAssets.ratingBlack,
                              height: 10,
                            )),
                      ),
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      onRatingUpdate: (rating) {
                        communicationRatingValue = (rating);
                        print(rating);
                      },
                    ),
                    15.verticalSpace,
                    const TextWidget(
                      text: "How would you rate Quality of product/Service?",
                      maxLines: 3,
                      fontweight: FontWeight.bold,
                      fontsize: 20,
                    ),
                    5.verticalSpace,
                    RatingBar(
                      initialRating: 0.0,
                      direction: Axis.horizontal,
                      itemSize: 30,
                      allowHalfRating: true,
                      itemCount: 5,
                      ratingWidget: RatingWidget(
                        full: Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(color: AppColors.primaryColor, shape: BoxShape.circle),
                            child: Image.asset(
                              // fit: BoxFit.cover,
                              AppAssets.ratingWhite,
                            )),
                       half: Container(
                            height: 75,
                            width: 75,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: Image.asset(
                              AppAssets.halfIcon,
                              fit: BoxFit.contain,
                            )),
                        empty: Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: BoxDecoration(color: AppColors.grey3, shape: BoxShape.circle),
                            child: Image.asset(
                              AppAssets.ratingBlack,
                              height: 10,
                            )),
                      ),
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      onRatingUpdate: (rating) {
                        qualityRatingValue = (rating);
                        print(rating);
                      },
                    ),
                    15.verticalSpace,
                    const TextWidget(
                      text: "How would you rate Quality of product/Service?",
                      maxLines: 3,
                      fontweight: FontWeight.bold,
                      fontsize: 20,
                    ),
                    5.verticalSpace,
                    RatingBar(
                      initialRating: 0.0,
                      direction: Axis.horizontal,
                      itemSize: 30,
                      allowHalfRating: true,
                      itemCount: 5,
                      ratingWidget: RatingWidget(
                        full: Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(color: AppColors.primaryColor, shape: BoxShape.circle),
                            child: Image.asset(
                              // fit: BoxFit.cover,
                              AppAssets.ratingWhite,
                            )),
                        half: Container(
                            height: 75,
                            width: 75,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: Image.asset(
                              AppAssets.halfIcon,
                              fit: BoxFit.contain,
                            )),
                        empty: Container(
                            height: 20,
                            width: 20,
                            padding: const EdgeInsets.all(5),
                            margin: const EdgeInsets.all(2),
                            decoration: BoxDecoration(color: AppColors.grey3, shape: BoxShape.circle),
                            child: Image.asset(
                              AppAssets.ratingBlack,
                              height: 10,
                            )),
                      ),
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      onRatingUpdate: (rating) {
                        valueForMoneyValue = (rating);
                        print(rating);
                      },
                    ),
                    40.verticalSpace,
                    const TextWidget(
                      text: "Care to share more about it?",
                      fontweight: FontWeight.bold,
                      fontsize: 20,
                    ),
                    20.verticalSpace,
                    AppTextField(
                      borderColor: AppColors.primaryColor,
                      inputTextStyle: const TextStyle(color: AppColors.blackColor),
                      controller: feedBackController,
                      maxLines: 5,
                      minLines: 3,
                      hintText: '',
                      hintStyle: TextStyle(color: AppColors.grey5),
                    ),
                    40.verticalSpace,
                    CommonButtonWidget(
                      innerPaddding: const EdgeInsets.all(15),
                      // padding: const EdgeInsets.all(10),
                      fontSize: 16,
                      textColor: AppColors.black,
                      fontWeight: FontWeight.w600,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: AppColors.primaryColor),
                      buttonColor: AppColors.primaryBgColor,
                      onTap: () {
                        feedBackAPI();
                      },
                      text: 'Publish Feedback',
                    ),
                    20.verticalSpace
                  ],
                ),
              ),
            ),
            ValueListenableBuilder(
              valueListenable: isLoading,
              builder: ((context, value, _) {
                if (isLoading.value) {
                  return Container(
                      color: AppColors.whiteColor,
                      height: MediaQuery.of(context).size.height,
                      child: Utility.progress());
                }
                return const SizedBox();
              }),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> feedBackAPI() async {
    final request = {
      'order_id': widget.orderId,
      'overall_experience_rating': getOverallRating,
      'communication_rating': getCommunicationRating,
      'quality_of_service_rating': getQualityRating,
      'value_of_service_rating': getValueRating,
      'comment': feedBackController.text,
      "freelancer_id": widget.freelancerId,
    };
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;
      CommonResponse responce =
          // ignore: use_build_context_synchronously
          CommonResponse.fromJson(await ApiManager().postCall(AppString.feedbackUrl, request, context));

      if (responce.status == '1') {
        isLoading.value = false;
        // ignore: use_build_context_synchronously
        showDialog(
          barrierDismissible: false,
          context: context,
          builder: (context) => AlertDialog(
            title: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [
                TextWidget(text: 'Thank You'),
              ],
            ),
            actions: [
              const TextWidget(
                text: 'By making your voice heard, you help us improve TRENDiPEOPLE',
                textAlign: TextAlign.center,
                maxLines: 3,
              ),
              20.verticalSpace,
            ],
          ),
        );
        Timer(
          const Duration(seconds: 3),
          (() {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
            Navigator.of(context).pop();
          }),
        );
      } else {
        isLoading.value = false;
      }
    } else {
      Utility.toast(message: 'No Internet Connection');
    }
  }

  double get getOverallRating {
    return (100 * overrallRatingValue) / 5;
  }

  double get getCommunicationRating {
    return (100 * communicationRatingValue) / 5;
  }

  double get getQualityRating {
    return (100 * qualityRatingValue) / 5;
  }

  double get getValueRating {
    return (100 * valueForMoneyValue) / 5;
  }
}
