import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:trendi_people/responce/comman_response.dart';
import 'package:trendi_people/screen/home.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/comman_button_widget.dart';

import '../model/category_model.dart';
import '../model/freelancer_detail_model.dart';
import '../model/user_model.dart';
import '../responce/simple_responce.dart';
import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/text_field_widget.dart';
import '../widget/text_widget.dart';

class ServiceSubmitScreen extends StatefulWidget {
  const ServiceSubmitScreen(
      {super.key,
      this.freelancerDetailModel,
      this.aviblityStatus,
      required this.catergoryList,
      this.startDate,
      this.deliveryOption});
  final FreelancerDetailModel? freelancerDetailModel;
  final String? aviblityStatus;
  final List<CategoryModel> catergoryList;
  final String? startDate;
  final String? deliveryOption;

  @override
  State<ServiceSubmitScreen> createState() => _ServiceSubmitScreenState();
}

class _ServiceSubmitScreenState extends State<ServiceSubmitScreen> {
  final fullNameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final addressController = TextEditingController();
  final dateTimeController = TextEditingController();
  final describeController = TextEditingController();
  String genderValue = '0';
  List<File> images = [];
  final _formKey = GlobalKey<FormState>();

  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  UserModel? user;

  @override
  void initState() {
    super.initState();
    fetchCustomerData();
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");
    fullNameController.text = '${user?.firstName ?? ""} ${user?.lastName ?? ""}';
    emailController.text = user?.email ?? "";
    if (user?.phoneNumber != null) phoneNumberController.text = user?.phoneNumber ?? "";

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(160.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 45,
                      width: 45,
                      child: Utility.imageLoader(
                        url: user?.profileImage ?? '',
                        isShapeCircular: true,
                        shape: BoxShape.circle,
                        placeholder: AppAssets.placeHolderImage,
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                const TextWidget(
                  text: "Please tell us more about yourself so that we can personalise your service.",
                  fontsize: 17,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        body: Form(
          key: _formKey,
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.only(left: 25, right: 25, top: 20, bottom: 25),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const TextWidget(
                        text: "Full name",
                        fontsize: 17,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      5.verticalSpace,
                      AppTextField(
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                          } else {
                            return 'Please enter full name';
                          }
                          return null;
                        },
                        borderColor: AppColors.primaryColor,
                        controller: fullNameController,
                      ),
                      10.verticalSpace,
                      const TextWidget(
                        text: "Email",
                        fontsize: 17,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      5.verticalSpace,
                      AppTextField(
                        borderColor: AppColors.primaryColor,
                        controller: emailController,
                      ),
                      10.verticalSpace,
                      const TextWidget(
                        text: "Phone Number",
                        fontsize: 17,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      5.verticalSpace,
                      AppTextField(
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                          } else {
                            return 'Please enter phone number';
                          }
                          return null;
                        },
                        keyboardType: TextInputType.number,
                        borderColor: AppColors.primaryColor,
                        controller: phoneNumberController,
                      ),
                      10.verticalSpace,
                      const TextWidget(
                        text: "Gender",
                        fontsize: 17,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: SizedBox(
                              child: RadioListTile(
                                // dense: true,
                                activeColor: AppColors.primaryColor,
                                value: '0',
                                groupValue: genderValue,
                                contentPadding: EdgeInsets.zero,
                                title: const TextWidget(
                                  text: 'Male',
                                  fontweight: FontWeight.normal,
                                  maxLines: 2,
                                  textcolor: AppColors.blackColor,
                                  fontsize: 12,
                                ),
                                onChanged: (String? newValue) {
                                  genderValue = newValue ?? "";
                                  _notify();
                                  print(newValue);
                                },
                              ),
                            ),
                          ),
                          Flexible(
                            child: SizedBox(
                              child: RadioListTile(
                                // dense: true,
                                activeColor: AppColors.primaryColor,
                                value: '1',
                                groupValue: genderValue,
                                contentPadding: EdgeInsets.zero,

                                title: const TextWidget(
                                  text: 'Women’s',
                                  fontweight: FontWeight.normal,
                                  maxLines: 2,
                                  textcolor: AppColors.blackColor,
                                  fontsize: 12,
                                ),
                                onChanged: (String? newValue) {
                                  genderValue = newValue ?? "";
                                  _notify();
                                  print(newValue);
                                },
                              ),
                            ),
                          ),
                          Flexible(
                            child: SizedBox(
                              child: RadioListTile(
                                // dense: true,
                                activeColor: AppColors.primaryColor,
                                value: '2',
                                groupValue: genderValue,
                                contentPadding: EdgeInsets.zero,
                                title: const TextWidget(
                                  text: 'Both',
                                  fontweight: FontWeight.normal,
                                  maxLines: 2,
                                  textcolor: AppColors.blackColor,
                                  fontsize: 12,
                                ),
                                onChanged: (String? newValue) {
                                  genderValue = newValue ?? "";
                                  _notify();
                                  print(newValue);
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      10.verticalSpace,
                      const TextWidget(
                        text: "Address",
                        fontsize: 17,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      5.verticalSpace,
                      AppTextField(
                        validator: (value) {
                          if (value != null && value.trim() != '') {
                          } else {
                            return 'Please enter address';
                          }
                          return null;
                        },
                        minLines: 3,
                        maxLines: 3,
                        borderColor: AppColors.primaryColor,
                        controller: addressController,
                      ),
                      10.verticalSpace,
                      const TextWidget(
                        text: "What's the best date(s)/time(s) to contact you to discuss the details of your request?",
                        fontsize: 17,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 3,
                      ),
                      5.verticalSpace,
                      AppTextField(
                        borderColor: AppColors.primaryColor,
                        controller: dateTimeController,
                        minLines: 3,
                        maxLines: 3,
                      ),
                      10.verticalSpace,
                      const TextWidget(
                        text:
                            "Please describe in as much detail as possible the type of item you would like us to make for you.",
                        fontsize: 17,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 3,
                      ),
                      5.verticalSpace,
                      AppTextField(
                        borderColor: AppColors.primaryColor,
                        controller: describeController,
                        minLines: 3,
                        maxLines: 3,
                      ),
                      10.verticalSpace,
                      Wrap(
                        spacing: 10,
                        runSpacing: 10,
                        alignment: WrapAlignment.start,
                        children: [
                          for (var image in images)
                            Stack(
                              alignment: Alignment.topRight,
                              children: [
                                Container(
                                  height: 102,
                                  width: 102,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    image: DecorationImage(
                                      image: FileImage(File(image.path)),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                    top: 5,
                                    right: 5,
                                  ),
                                  child: GestureDetector(
                                    onTap: () {
                                      images.remove(image);
                                      _notify();
                                    },
                                    child: const Icon(
                                      Icons.cancel_outlined,
                                      color: Colors.black,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          GestureDetector(
                            onTap: () {
                              getimage(false);
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey, style: BorderStyle.solid),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(15),
                                child: Image.asset(
                                  AppAssets.cameraIcon,
                                  color: AppColors.black,
                                  height: 70,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                      10.verticalSpace,
                      CommonButtonWidget(
                        text: 'Submit',
                        onTap: () {
                          if (_formKey.currentState!.validate()) {
                            submitRequest();
                          }
                        },
                      )
                    ],
                  ),
                ),
              ),
              ValueListenableBuilder(
                valueListenable: isLoading,
                builder: ((context, value, _) {
                  if (isLoading.value) {
                    return Container(
                        color: AppColors.whiteColor,
                        height: MediaQuery.of(context).size.height,
                        child: Utility.progress());
                  }
                  return const SizedBox();
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future getimage(bool camera) async {
    final image = await ImagePicker().pickImage(
      source: camera ? ImageSource.camera : ImageSource.gallery,
    );
    //var length = await videox.d
    //print(length);
    if (image == null) {
      log("error");
    } else {
      images.add(File(image.path));
      _notify();
    }
  }

  String? get genderName {
    if (genderValue == '0') {
      return 'MALE';
    } else if (genderValue == '1') {
      return 'Women’s';
    } else if (genderValue == '2') {
      return 'Both';
    }
    return null;
  }

  submitRequest() async {
    log(images.length.toString());
    final request = <String, String>{
      'name': fullNameController.text,
      'email': emailController.text,
      'phone_number': phoneNumberController.text,
      'address': addressController.text,
      'notes': dateTimeController.text,
      'gender': genderName ?? "",
      'description': describeController.text,
      'freelancer_id': widget.freelancerDetailModel?.id.toString() ?? "",
      'start_date': widget.startDate ?? '',
      'availability': widget.aviblityStatus ?? "",
      if (widget.catergoryList.isNotEmpty)
        for (int i = 0; i < widget.catergoryList.length; i++) 'categories[$i]': widget.catergoryList[i].id.toString(),
      'delivery_option': widget.deliveryOption ?? "",
    };

    final files = [
      for (int i = 0; i < images.length; i++)
        MapEntry(
          'photos[$i]',
          images[i],
        ),
    ];
    // final List<MapEntry<String, File>> files = [
    //   if (profileImageFile.value != null) MapEntry('profile_image', profileImageFile.value!)
    // ];
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;

      CommonResponse response = CommonResponse.fromJson(
          await ApiManager().multipartRequest(url: AppString.submitOrderRequestUrl, request: request, files: files));
      if (response.status == "1") {
        isLoading.value = false;
        // ignore: use_build_context_synchronously
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const Home(),
          ),
        );
        return Utility.toast(message: response.message);
      } else {
        isLoading.value = false;
        return Utility.toast(message: response.message);
      }
    } else {
      isLoading.value = false;
      return Utility.toast(message: 'No Internet Connection');
    }
  }
}
