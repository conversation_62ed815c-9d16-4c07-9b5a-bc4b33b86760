// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:trendi_people/model/skills_model.dart';
import 'package:trendi_people/model/user_model.dart';
import 'package:trendi_people/responce/report_response.dart';
import 'package:trendi_people/utility/api_manager.dart';
import 'package:trendi_people/utility/app_string.dart';

import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/utility.dart';
import '../widget/alert_dialog_box_widget.dart';

class ViewFullImage extends StatefulWidget {
  ViewFullImage({
    Key? key,
    required this.index,
    required this.images,
    this.imageId,
    this.userId,
    this.skills,
  }) : super(key: key);
  final int index;
  final List<String> images;
  int? imageId;
  final int? userId;
  final Skills? skills;

  @override
  State<ViewFullImage> createState() => _ViewFullImageState();
}

class _ViewFullImageState extends State<ViewFullImage> {
  PageController controller = PageController();
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  UserModel? user;
  final reportController = TextEditingController();
  @override
  void initState() {
    super.initState();
    log("${widget.imageId}imageid");
    log("${widget.userId}userId");
    fetchCustomerData();
    controller = PageController(initialPage: widget.index);
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    // log(user?.firstName ?? "");

    _notify();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> reportApi() async {
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;
      var request = <String, dynamic>{};
      // request['user_id'] = widget.userId.toString();
      request['user_file_id'] = widget.imageId.toString();
      request['description'] = reportController.text.trim();

      ReportResponse response =
          // ignore: use_build_context_synchronously
          ReportResponse.fromJson(await ApiManager().postCall(AppString.reportUrl, request, context));
      if (response.status == "1") {
        isLoading.value = false;
      }
      reportController.clear();
      isLoading.value = false;
      Utility.toast(message: response.message);
    } else {
      isLoading.value = false;
      Utility.toast(message: 'No Internet Connection');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(95.0),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  margin: const EdgeInsets.all(10),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade100),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.arrow_back,
                    color: AppColors.black,
                  ),
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (context) {
                      return AlertDialogBox(
                          ontapYes: () {
                            if (reportController.text.trim().isEmpty) {
                              Utility.toast(message: 'Please enter something');
                            } else {
                              reportApi();
                              Navigator.pop(context);
                            }
                          },
                          alertMessage: 'By reporting this photo, we shall act on this in 24 hours.',
                          buttonTextYes: 'Report',
                          title: 'Report Image',
                          isTextField: true,
                          controller: reportController,
                          ontapNo: () {
                            Navigator.pop(context);
                          },
                          buttonTextNo: 'Cancel');
                    },
                  );
                },
                child: const Padding(
                    padding: EdgeInsets.all(15.0),
                    child: Icon(
                      Icons.report,
                      color: Colors.black,
                    )),
              )
            ],
          ),
        ),
        body: Center(
          child: PageView.builder(
            controller: controller,
            itemCount: widget.skills?.files?.length,
            onPageChanged: (val) {
              widget.imageId = widget.skills?.files?[val].id;
              _notify();
            },
            itemBuilder: (context, index) {
              log(widget.skills?.files?[index].toString() ?? "");
              return InteractiveViewer(
                minScale: 0.1,
                maxScale: 5,
                child: SizedBox(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                    child: Utility.imageLoader(
                      url: widget.skills?.files?[index].filePath.toString() ?? "",
                      fit: BoxFit.contain,
                      placeholder: AppAssets.placeHolderImage,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
