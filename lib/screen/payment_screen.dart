import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:trendi_people/responce/simple_responce.dart';
import 'package:trendi_people/utility/api_manager.dart';
import 'package:trendi_people/utility/app_colors.dart';
import 'package:trendi_people/utility/country.dart';
import 'package:trendi_people/utility/string_extension.dart';
import 'package:trendi_people/utility/utility.dart';
import 'package:trendi_people/widget/app_drop_down_widget.dart';
import 'package:trendi_people/widget/comman_button_widget.dart';
import 'package:trendi_people/widget/text_field_widget.dart';
import 'package:trendi_people/widget/text_widget.dart';

import '../model/category_model.dart';
import '../model/freelancer_detail_model.dart';
import '../model/user_model.dart';
import '../utility/app_string.dart';
import 'booking_conform_screen.dart';
import 'home.dart';

class PaymentScreen extends StatefulWidget {
  const PaymentScreen({
    super.key,
    this.freelancerDetailModel,
    this.aviblityStatus,
    required this.catergoryList,
    this.startDate,
    this.deliveryOption,
    this.gender,
    this.price,
  });
  final FreelancerDetailModel? freelancerDetailModel;
  final String? aviblityStatus;
  final List<CategoryModel> catergoryList;
  final String? startDate;
  final String? deliveryOption;
  final String? gender;
  final String? price;
  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final cardNumberController = TextEditingController(text: '4000 0025 0000 3155'.isDebuging);
  final expiryDateController = TextEditingController(text: '02/25'.isDebuging);
  final cvcController = TextEditingController(text: '123'.isDebuging);
  final cardHolderNameController = TextEditingController(text: 'sdfhdf'.isDebuging);
  final aptController = TextEditingController();
  final postalCodeController = TextEditingController(text: '324234'.isDebuging);
  final cityController = TextEditingController();
  final townController = TextEditingController();
  final selectedCountryIsoCode = ValueNotifier<String?>(null);
  final _formKey = GlobalKey<FormState>();
  final isLoading = ValueNotifier<bool>(false);
  final stripeKey = AppString.stripeKey;

  MaskTextInputFormatter maskFormattercardnumber =
      MaskTextInputFormatter(mask: 'X### #### #### ####', filter: {'#': RegExp(r'[0-9]'), 'X': RegExp(r'[1-9]')});
  MaskTextInputFormatter expiryDateFormatter = MaskTextInputFormatter(mask: 'XX/XX', filter: {'X': RegExp(r'[0-9]')});

  UserModel? user;

  @override
  void initState() {
    super.initState();
    fetchCustomerData();
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(100.0),
          child: Padding(
            padding:
                //  const EdgeInsets.all(0),
                const EdgeInsets.only(
              left: 25,
              right: 25,
              top: 20,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade100),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.arrow_back,
                      color: AppColors.black,
                    ),
                  ),
                ),
                const TextWidget(
                  text: "Payment",
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                const SizedBox(
                  width: 50,
                )
              ],
            ),
          ),
        ),
        body: Stack(
          children: [
            Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Text(
                        'Payment Card Details',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(left: 12.0),
                      child: TextWidget(
                        text: 'Card number *',
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: AppTextField(
                        controller: cardNumberController,
                        borderColor: AppColors.primaryColor,
                        inputFormatters: [maskFormattercardnumber],
                        maxlength: 19,
                        counterText: '',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Enter card number';
                          }
                          if (value.length != 19) {
                            return 'Enter valid card number';
                          }
                          return null;
                        },
                      ),
                    ),
                    Row(
                      children: [
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(left: 12.0),
                                child: TextWidget(
                                  text: 'Expiry Date *',
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(10),
                                child: AppTextField(
                                  controller: expiryDateController,
                                  borderColor: AppColors.primaryColor,
                                  inputFormatters: [expiryDateFormatter],
                                  maxlength: 5,
                                  counterText: '',
                                  hintText: 'MM/YY',
                                  hintStyle: TextStyle(color: AppColors.grey4),
                                  validator: (value) {
                                    if (value == null || value.trim().isEmpty) {
                                      return 'Enter expiry date';
                                    }
                                    if (value.length != 5) {
                                      return 'Enter valid expiry date';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(left: 12.0),
                                child: TextWidget(
                                  text: 'CVC *',
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(10),
                                child: AppTextField(
                                  controller: cvcController,
                                  borderColor: AppColors.primaryColor,
                                  maxlength: 3,
                                  counterText: '',
                                  validator: (value) {
                                    if (value == null || value.trim().isEmpty) {
                                      return 'Enter CVC date';
                                    }
                                    if (value.length != 3) {
                                      return 'Enter valid CVC';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Text(
                        'Billing Details',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(left: 12.0),
                      child: TextWidget(
                        text: "Card holder's name *",
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: AppTextField(
                        controller: cardHolderNameController,
                        borderColor: AppColors.primaryColor,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return "Enter card holder's name";
                          }
                          return null;
                        },
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(left: 12.0),
                      child: TextWidget(
                        text: "Postcode *",
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: AppTextField(
                        controller: postalCodeController,
                        borderColor: AppColors.primaryColor,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return "Enter postcode";
                          }
                          return null;
                        },
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(left: 12.0),
                      child: TextWidget(
                        text: "Town (optional)",
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: AppTextField(
                        controller: townController,
                        borderColor: AppColors.primaryColor,
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(left: 12.0),
                      child: TextWidget(
                        text: "Country *",
                      ),
                    ),
                    ValueListenableBuilder<String?>(
                      valueListenable: selectedCountryIsoCode,
                      builder: (context, isoCode, _) {
                        return Padding(
                          padding: const EdgeInsets.all(10),
                          child: AppDropDown(
                            borderColor: AppColors.primaryColor,
                            selectedValue: isoCode,
                            items: countries
                                .map((e) => DropdownMenuItem(
                                      value: e['isoCode'],
                                      child: Text(
                                        e['name'] ?? '',
                                      ),
                                    ))
                                .toList(),
                            onSelect: (String? newvalueSelected) {
                              selectedCountryIsoCode.value = newvalueSelected!;
                            },
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return "Select country";
                              }
                              return null;
                            },
                          ),
                        );
                      },
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: CommonButtonWidget(
                        text: 'Pay',
                        onTap: () {
                          if (_formKey.currentState?.validate() ?? false) {
                            pay();
                          }
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
            ValueListenableBuilder<bool>(
              valueListenable: isLoading,
              builder: (context, loading, _) {
                if (loading) {
                  return Utility.progress();
                }
                return const SizedBox();
              },
            ),
          ],
        ),
      ),
    );
  }

  void pay() async {
    if (await ApiManager.checkInternet()) {
      try {
        isLoading.value = true;
        Stripe.publishableKey = stripeKey;
        Stripe.merchantIdentifier = 'merchant.flutter.stripe.test';
        Stripe.urlScheme = 'flutterstripe';
        // Stripe.instance.handleURLCallback(url)
        var cardInfo = CardDetails(
          number: cardNumberController.text.trim(),
          cvc: cvcController.text,
          expirationMonth: int.parse(expiryDateController.text.trim().split('/').first),
          expirationYear: int.parse(expiryDateController.text.trim().split('/').last),
        );
        await Stripe.instance.applySettings();
        // await beignCheckOutAPi();
        await Stripe.instance.dangerouslyUpdateCardDetails(cardInfo);
        PaymentMethod? paymentMethod;
        await Stripe.instance
            .createPaymentMethod(
          params: const PaymentMethodParams.card(
            paymentMethodData: PaymentMethodData(),
          ),
        )
            .then((value) {
          paymentMethod = value;
        }).catchError((e) {
          isLoading.value = false;
          log(e.error.localizedMessage.toString());
          Utility.toast(message: e.error.localizedMessage.toString());
        });

        if (paymentMethod?.id != null && paymentMethod!.id.isNotEmpty) {
          placeOrder(paymentMethod!.id);
        } else {
          isLoading.value = false;
        }
      } catch (e, s) {
        Utility.toast(message: e.toString());
        isLoading.value = false;
        log('ERROR________--------');
        log(e.toString());
        log(s.toString());
      }
    } else {
      isLoading.value = false;
      Utility.toast(message: 'No Internet');
    }
  }

  void placeOrder(String paymentMethodId) async {
    final request = <String, String>{
      'name': cardHolderNameController.text,
      'email': user?.email ?? "",
      'phone_number': user?.phoneNumber ?? "",
      'address': user?.address ?? '',
      'freelancer_id': widget.freelancerDetailModel?.id.toString() ?? "",
      'start_date': widget.startDate ?? '',
      'availability': widget.aviblityStatus ?? "",
      'delivery_option': widget.deliveryOption ?? "",
      'gender': widget.gender ?? "",
      'order_price': widget.price ?? "",
      'payment_method_id': paymentMethodId,
      if (widget.catergoryList.isNotEmpty)
        for (int i = 0; i < widget.catergoryList.length; i++) 'categories[$i]': widget.catergoryList[i].id.toString(),
    };

    if (await ApiManager.checkInternet()) {
      isLoading.value = true;

      SimpleResponse response = SimpleResponse.fromJson(await ApiManager().multipartRequest(
        url: AppString.submitOrderRequestUrl,
        request: request,
      ));
      if (response.status == "1") {
        isLoading.value = false;
        // ignore: use_build_context_synchronously
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const Home(),
          ),
        );
        Utility.toast(message: response.message);
      } else if (response.status == '2' && response.data != null) {
        try {
          final intant = await Stripe.instance
              .confirmPayment(
                  paymentIntentClientSecret: response.data!,
                  data: const PaymentMethodParams.card(
                    paymentMethodData: PaymentMethodData(),
                  ))
              .catchError((e) {
            isLoading.value = false;
            log(e.error.localizedMessage.toString());
            Utility.toast(message: e.error.localizedMessage.toString());
          });
          isLoading.value = false;
          if (intant.status == PaymentIntentsStatus.Succeeded) {
            // ignore: use_build_context_synchronously
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const BookingConformSCreen(),
              ),
            );
          }
        } catch (e) {}
      } else {
        isLoading.value = false;
        return Utility.toast(message: response.message);
      }
    } else {
      isLoading.value = false;
      return Utility.toast(message: 'No Internet Connection');
    }
  }
}
