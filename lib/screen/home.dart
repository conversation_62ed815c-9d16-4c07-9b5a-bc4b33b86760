import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:trendi_people/screen/booking_screen.dart';
import 'package:trendi_people/screen/frelincer_service_screen.dart';
import 'package:trendi_people/screen/notification_screen.dart';
import 'package:trendi_people/screen/profile_deatil_screen.dart';
import 'package:trendi_people/utility/firebase_messaging_services.dart';

import '../model/user_model.dart';
import '../responce/notification_app_responce.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/lazy_stack_index.dart';
import '../utility/utility.dart';
import '../widget/comman_buttom_button_widget.dart';
import 'chat/chat_detail_screen.dart';
import 'chat_room_screen.dart';

class Home extends StatefulWidget {
  const Home({super.key, this.initialIndex});
  final int? initialIndex;

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  final currentIndex = ValueNotifier<int>(0);
  UserModel? user;
  @override
  void initState() {
    super.initState();
    FirebaseMessagingService().initialize(
        context: context,
        onMessage: (message) {
          final notification = NotificationAppResponce.fromJson(jsonDecode(message.data['data']));
          if (notification.clickAction != null) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => ChatDetailScreen(
                  otherUserId: notification.userData?.id ?? 0,
                  otherUserName: notification.userData?.name,
                ),
              ),
            );
          }
        });
    fetchCustomerData();
    if (widget.initialIndex != null) {
      currentIndex.value = widget.initialIndex ?? 0;
    }
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: currentIndex,
      builder: (BuildContext context, index, _) {
        return Scaffold(
          backgroundColor: AppColors.whiteColor,
          body: SafeArea(
            child: Stack(
              children: [
                LazyIndexedStack(
                  index: index,
                  children: const [
                    FrelincerServiceScreen(),
                    BookingScreen(),
                    ChatRoomScreen(),
                    NotificationScreen(),
                    ProfileDetailScreen(),
                  ],
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            height: kBottomNavigationBarHeight,
            padding: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              color: AppColors.whiteColor,
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withOpacity(0.15),
                  offset: const Offset(0, -4),
                  spreadRadius: 3,
                  blurRadius: 10,
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                CommonBottomButtonWidget(
                  title: 'Home',
                  cureentIndex: 0,
                  selectedIndex: index,
                  image: AppAssets.homeIcon,
                  onTap: () {
                    currentIndex.value = 0;
                  },
                ),
                CommonBottomButtonWidget(
                  title: 'My Bookings',
                  cureentIndex: 1,
                  selectedIndex: index,
                  image: AppAssets.documentIcon,
                  onTap: () {
                    currentIndex.value = 1;
                  },
                ),
                CommonBottomButtonWidget(
                  title: 'Messages',
                  cureentIndex: 2,
                  selectedIndex: index,
                  image: AppAssets.messageIcon,
                  onTap: () {
                    currentIndex.value = 2;
                  },
                ),
                CommonBottomButtonWidget(
                  title: 'Notifications',
                  cureentIndex: 3,
                  selectedIndex: index,
                  image: AppAssets.notificationIcon,
                  onTap: () {
                    currentIndex.value = 3;
                  },
                ),
                CommonBottomButtonWidget(
                  title: 'Profile',
                  cureentIndex: 4,
                  selectedIndex: index,
                  image: user?.profileImage ?? "",
                  onTap: () {
                    currentIndex.value = 4;
                  },
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
