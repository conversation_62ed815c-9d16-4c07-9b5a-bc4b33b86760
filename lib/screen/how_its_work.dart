import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../utility/app_colors.dart';

class HowItsWorksScreen extends StatefulWidget {
  const HowItsWorksScreen({super.key});

  @override
  State<HowItsWorksScreen> createState() => _HowItsWorksScreenState();
}

class _HowItsWorksScreenState extends State<HowItsWorksScreen> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          appBar: AppBar(
            backgroundColor: AppColors.primaryColor,
            centerTitle: true,
            title: const Text("How It's Works", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          ),
          body: InAppWebView(
              onLoadStart: (controller, url) async {
                setState(() {});
              },
              initialUrlRequest: URLRequest(
                url: Uri.parse('https://trendipeople.com/how-it-works/'),
              ))),
    );
  }
}
