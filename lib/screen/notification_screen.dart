import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../model/notification_model.dart';
import '../model/user_model.dart';
import '../responce/notification_reponce.dart';
import '../utility/api_manager.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/text_widget.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  int page = 0;
  int perPage = 10;
  bool stop = false;
  List<NotificationModel?> notification = [];
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isPageLoading = ValueNotifier<bool>(false);
  UserModel? user;

  @override
  void initState() {
    super.initState();
    getNotification(isFirst: true);
    fetchCustomerData();
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  void noDataLogic(pageNumber) {
    page = pageNumber - 1;
    stop = true;
    _notify();
  }

  _refresh() {
    notification.clear();
    page = 0;
    stop = false;
    _notify();
    getNotification(isFirst: true);
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  Future<void> getNotification({required bool isFirst}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    if (await ApiManager.checkInternet()) {
      page += 1;
      var request = <String, dynamic>{};
      request['page'] = page.toString();
      request['per_page'] = perPage.toString();
      NotificationReponce response = NotificationReponce.fromJson(
        await ApiManager().getCall(AppString.notificationList, request, context),
      );
      if (response.status == "1" && response.data != null && response.data!.isNotEmpty) {
        notification.addAll(response.data!);

        _notify();
      } else {
        noDataLogic(page);
      }
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
    } else {
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
      Utility.toast(message: 'No Internet Connection');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        color: AppColors.primaryColor,
        onRefresh: () async {
          _refresh();
        },
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 25, right: 25, top: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const TextWidget(
                        text: "Your Notifications",
                        fontsize: 30,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      10.verticalSpace,
                    ],
                  ),
                  Flexible(
                    child: ListView.separated(
                      separatorBuilder: (context, index) => const SizedBox(height: 8),
                      itemCount: notification.length,
                      itemBuilder: (context, index) {
                        return (notification.length - 1) == index
                            ? VisibilityDetector(
                                key: Key(index.toString()),
                                onVisibilityChanged: (VisibilityInfo info) {
                                  log(notification.length.toString());
                                  if (!stop &&
                                      index == (notification.length - 1) &&
                                      !isLoading.value &&
                                      !isPageLoading.value) {
                                    getNotification(isFirst: false);
                                  }
                                },
                                child: Column(
                                  children: [
                                    notificationItemView(index),
                                    ValueListenableBuilder<bool>(
                                      valueListenable: isPageLoading,
                                      builder: (context, value, _) {
                                        if (isPageLoading.value) {
                                          return Column(
                                            children: [
                                              const SizedBox(height: 10),
                                              Utility.progress(),
                                              const SizedBox(height: 10),
                                            ],
                                          );
                                        }
                                        return const SizedBox();
                                      },
                                    )
                                  ],
                                ),
                              )
                            : notificationItemView(index);
                      },
                    ),
                  ),
                ],
              ),
            ),
            ValueListenableBuilder<bool>(
              valueListenable: isLoading,
              builder: (BuildContext context, value, _) {
                if (value) {
                  return Utility.progress();
                } else if (!value && notification.isEmpty) {
                  return Utility.noDataWidget(
                    text: 'No Notification',
                  );
                }
                return const SizedBox();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget notificationItemView(int index) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.primaryColor,
          ),
          borderRadius: BorderRadius.circular(10)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
              child: TextWidget(text: notification[index]?.title ?? "", fontsize: 18, fontweight: FontWeight.bold)),
          10.verticalSpace,
          Flexible(
            child: TextWidget(
                text: (notification[index]?.message ?? ""), fontsize: 16, fontweight: FontWeight.normal, maxLines: 10),
          ),
        ],
      ),
    );
  }
}
