import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';

import '../model/order_detail_model.dart';
import '../model/user_model.dart';
import '../responce/order_detail_responce.dart';
import '../utility/api_manager.dart';
import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/app_string.dart';
import '../utility/utility.dart';
import '../widget/comman_button_widget.dart';
import '../widget/text_field_widget.dart';
import '../widget/text_widget.dart';
import 'feedback_screen.dart';

class OrderDetailsScreen extends StatefulWidget {
  const OrderDetailsScreen({super.key, required this.id});
  final int id;
  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  UserModel? user;
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  final feedBackController = TextEditingController();
  OrderDetailModel? orderDetailModel;
  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    orderDetailApi(widget.id);
    fetchCustomerData();
  }

  Future<void> fetchCustomerData() async {
    user = await Utility.getUser();
    log(user?.firstName ?? "");

    _notify();
  }

  @override
  Widget build(BuildContext context) {
    feedBackController.text = orderDetailModel?.review?.comment ?? "";
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.whiteColor,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(170.0),
          child: Padding(
            padding: const EdgeInsets.only(left: 25, right: 25, top: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade100),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    Container(
                      height: 45,
                      width: 45,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                      ),
                      child: Utility.imageLoader(
                        url: user?.profileImage ?? '',
                        isShapeCircular: true,
                        shape: BoxShape.circle,
                        placeholder: AppAssets.placeHolderImage,
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                const TextWidget(
                  text: 'Order Detail',
                  fontsize: 30,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                // 10.verticalSpace,
                // TextWidget(
                //   text: 'Order Detail Screen',
                //   fontsize: 16,
                //   overflow: TextOverflow.ellipsis,
                //   maxLines: 2,
                //   textcolor: AppColors.grey4,
                //   fontweight: FontWeight.w400,
                // ),
              ],
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: ValueListenableBuilder(
            valueListenable: isLoading,
            builder: ((context, value, _) {
              if (value) {
                return Container(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  color: AppColors.whiteColor,
                  child: Utility.progress(),
                );
              }
              return Column(
                children: [
                  Container(
                    margin: const EdgeInsets.all(10),
                    padding: const EdgeInsets.all(20),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10), border: Border.all(color: AppColors.primaryColor)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextWidget(text: 'Order #${orderDetailModel?.id}', fontsize: 23),
                        10.verticalSpace,
                        SizedBox(
                          height: 100,
                          width: 100,
                          child: Utility.imageLoader(
                            isShapeCircular: true,
                            shape: BoxShape.circle,
                            url: orderDetailModel?.freelancer?.profileImage ?? "",
                            placeholder: AppAssets.placeHolderImage,
                          ),
                        ),
                        20.verticalSpace,
                        const TextWidget(text: 'Maker:', fontsize: 15, fontweight: FontWeight.normal),
                        10.verticalSpace,
                        TextWidget(
                            text:
                                '${orderDetailModel?.freelancer?.firstName ?? ""} ${orderDetailModel?.freelancer?.lastName ?? ""}',
                            fontsize: 20,
                            fontweight: FontWeight.bold,
                            textcolor: AppColors.primaryColor),
                        10.verticalSpace,
                        const TextWidget(text: 'Date:', fontsize: 15, fontweight: FontWeight.normal),
                        10.verticalSpace,
                        TextWidget(
                            text: orderDetailModel?.startDate ?? "",
                            fontsize: 15,
                            fontweight: FontWeight.bold,
                            textcolor: AppColors.primaryColor),
                        10.verticalSpace,
                      ],
                    ),
                  ),
                  20.verticalSpace,
                  Container(
                    margin: const EdgeInsets.all(10),
                    padding: const EdgeInsets.all(20),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10), border: Border.all(color: AppColors.primaryColor)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const TextWidget(text: 'Category', fontsize: 23),
                        // 10.verticalSpace,
                        // const TextWidget(text: '', fontsize: 15, fontweight: FontWeight.normal),
                        10.verticalSpace,
                        TextWidget(
                            text:
                                '${orderDetailModel?.mainCategory?.name ?? ""} - ${orderDetailModel?.categories?.first.name ?? ""}',
                            fontsize: 20,
                            maxLines: 4,
                            fontweight: FontWeight.bold,
                            textcolor: AppColors.primaryColor),
                        10.verticalSpace,
                      ],
                    ),
                  ),
                  20.verticalSpace,
                  Container(
                    margin: const EdgeInsets.all(10),
                    padding: const EdgeInsets.all(20),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10), border: Border.all(color: AppColors.primaryColor)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const TextWidget(text: 'Order Detail', fontsize: 23),
                        10.verticalSpace,
                        const TextWidget(text: 'Total Price:', fontsize: 15, fontweight: FontWeight.normal),
                        10.verticalSpace,
                        TextWidget(
                            text: orderDetailModel?.totalPrice ?? "-",
                            fontsize: 20,
                            fontweight: FontWeight.bold,
                            textcolor: AppColors.primaryColor),
                      ],
                    ),
                  ),
                  20.verticalSpace,
                  if (orderDetailModel?.review != null) ...[
                    Container(
                      margin: const EdgeInsets.all(10),
                      padding: const EdgeInsets.all(20),
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10), border: Border.all(color: AppColors.primaryColor)),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const TextWidget(
                            text: "How did we do?",
                            fontweight: FontWeight.bold,
                            fontsize: 20,
                          ),
                          10.verticalSpace,
                          RatingBar(
                            initialRating: orderDetailModel?.review?.rating ?? 0.0,
                            direction: Axis.horizontal,
                            itemSize: 30,
                            allowHalfRating: true,
                            ignoreGestures: true,
                            itemCount: 5,
                            ratingWidget: RatingWidget(
                              full: Container(
                                  height: 20,
                                  width: 20,
                                  padding: const EdgeInsets.all(5),
                                  margin: const EdgeInsets.all(2),
                                  decoration:
                                      const BoxDecoration(color: AppColors.primaryColor, shape: BoxShape.circle),
                                  child: Image.asset(
                                    // fit: BoxFit.cover,
                                    AppAssets.ratingWhite,
                                  )),
                              half: Container(
                                  height: 75,
                                  width: 75,
                                  padding: const EdgeInsets.all(5),
                                  margin: const EdgeInsets.all(2),
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                  ),
                                  child: Image.asset(
                                    AppAssets.halfIcon,
                                    fit: BoxFit.contain,
                                  )),
                              empty: Container(
                                  height: 20,
                                  width: 20,
                                  padding: const EdgeInsets.all(5),
                                  margin: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(color: AppColors.grey3, shape: BoxShape.circle),
                                  child: Image.asset(
                                    AppAssets.ratingBlack,
                                    height: 10,
                                  )),
                            ),
                            itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                            onRatingUpdate: (rating) {
                              print(rating);
                            },
                          ),
                          20.verticalSpace,
                          const TextWidget(
                            text: "Your review",
                            fontweight: FontWeight.bold,
                            fontsize: 20,
                          ),
                          10.verticalSpace,
                          AppTextField(
                            readOnly: true,
                            borderColor: AppColors.primaryColor,
                            inputTextStyle: const TextStyle(color: AppColors.blackColor),
                            controller: feedBackController,
                            maxLines: 5,
                            minLines: 3,
                            hintText: '',
                            hintStyle: TextStyle(color: AppColors.grey5),
                          ),
                        ],
                      ),
                    ),
                  ],
                  if (orderDetailModel?.review == null)
                    CommonButtonWidget(
                      innerPaddding: const EdgeInsets.all(15),
                      padding: const EdgeInsets.all(10),
                      fontSize: 16,
                      textColor: AppColors.black,
                      fontWeight: FontWeight.w600,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: AppColors.primaryColor),
                      buttonColor: AppColors.primaryBgColor,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => FeedBackScreen(
                                  orderId: orderDetailModel?.id.toString() ?? "",
                                  freelancerId: orderDetailModel?.freelancerId.toString() ?? ""),
                            ));
                      },
                      text: 'Share Feedback',
                    ),
                ],
              );
            }),
          ),
        ),
      ),
    );
  }

  Future<void> orderDetailApi(int id) async {
    if (await ApiManager.checkInternet()) {
      isLoading.value = true;
      var request1 = <String, dynamic>{};

      OrderDetailResponce response =
          OrderDetailResponce.fromJson(await ApiManager().getCall(AppString.getOrderDetailUrl(id), request1, context));

      if (response.status == "1") {
        orderDetailModel = response.data;
        _notify();
        isLoading.value = false;
        log(orderDetailModel?.id.toString() ?? "null");
      } else {
        isLoading.value = false;
        Utility.toast(message: response.message);
      }
    } else {
      Utility.toast(message: 'noInternetConnection');
    }
  }
}
