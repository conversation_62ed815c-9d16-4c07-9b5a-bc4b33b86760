// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

import '../utility/app_colors.dart';

class AppDropDown extends StatelessWidget {
  const AppDropDown({
    Key? key,
    required this.onSelect,
    this.validator,
    this.selectedValue,
    this.hintText,
    this.borderColor,
    this.focusNode,
    required this.items,
  }) : super(key: key);
  final Function(String? valueOfCategory) onSelect;
  final String? Function(String?)? validator;

  final String? selectedValue;
  final String? hintText;
  final Color? borderColor;

  final FocusNode? focusNode;
  final List<DropdownMenuItem<String>>? items;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      isExpanded: true,
      borderRadius: BorderRadius.circular(10),
      value: selectedValue,
      validator: validator,
      focusNode: focusNode,
      icon: const Icon(Icons.keyboard_arrow_down_sharp),
      onChanged: (v) {
        onSelect(v);
      },
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.only(left: 15, right: 15),
        hintText: hintText,
        hintStyle: const TextStyle(color: Colors.grey),
        focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: borderColor ?? AppColors.grey4,
            )),
        disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: borderColor ?? AppColors.grey4,
            )),
        errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: borderColor ?? AppColors.grey4,
            )),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: borderColor ?? AppColors.grey4,
            )),
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: borderColor ?? AppColors.grey4,
            )),
      ),
      items: items,
    );
  }
}
