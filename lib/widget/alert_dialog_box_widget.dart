import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_field_widget.dart';

import '../utility/app_colors.dart';
import 'comman_button_widget.dart';

class AlertDialogBox extends StatelessWidget {
  const AlertDialogBox(
      {Key? key,
      required this.ontapYes,
      required this.alertMessage,
      required this.buttonTextYes,
      this.icon,
      required this.title,
      required this.ontapNo,
      required this.buttonTextNo,
      this.isTextField = false,
      this.controller,
      this.fontSize})
      : super(key: key);
  final Function() ontapYes;
  final Function() ontapNo;
  final String alertMessage;
  final String buttonTextYes;
  final String buttonTextNo;
  final IconData? icon;
  final String title;
  final double? fontSize;
  final bool isTextField;
  final TextEditingController? controller;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: const OutlineInputBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(20),
        ),
      ),
      backgroundColor: AppColors.whiteColor,
      title: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: AppColors.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
              maxLines: 2,
            ),
          ),
          const SizedBox(
            width: 5,
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              alertMessage,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: fontSize ?? 18),
              textAlign: TextAlign.center,
            ),
            if (isTextField && controller != null)
              AppTextField(
                padding: const EdgeInsets.symmetric(vertical: 20),
                maxLines: 3,
                controller: controller!,
                borderColor: AppColors.black,
                // hintText: 'Enter something',
                // hintStyle: TextStyle(color: AppColors.grey4),
              ),
            10.verticalSpace,
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: CommonButtonWidget(
                    borderRadius: BorderRadius.circular(5),
                    innerPaddding: const EdgeInsets.all(12),
                    maxwidth: 100,
                    onTap: ontapYes,
                    text: buttonTextYes,
                    fontSize: fontSize ?? 18,
                  ),
                ),
                20.horizontalSpace,
                Flexible(
                  child: CommonButtonWidget(
                    borderRadius: BorderRadius.circular(5),
                    innerPaddding: const EdgeInsets.all(12),
                    maxwidth: 90,
                    onTap: ontapNo,
                    text: buttonTextNo,
                    fontSize: fontSize ?? 18,
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
