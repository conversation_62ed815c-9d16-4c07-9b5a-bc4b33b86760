import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../utility/app_colors.dart';

class TextWidget extends StatelessWidget {
  final String text;
  final int? maxLines;
  final double? fontsize;
  final FontStyle? fontStyle;
  final Color? textcolor;
  final FontWeight? fontweight;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final double? letterSpacing;
  final TextDecoration? textDecoration;
  const TextWidget(
      {Key? key,
      required this.text,
      this.fontsize,
      this.maxLines,
      this.fontStyle,
      this.textcolor,
      this.fontweight,
      this.textAlign,
      this.letterSpacing,
      this.textDecoration,
      this.overflow})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      maxLines: maxLines,
      style: GoogleFonts.roboto(
          textStyle: TextStyle(
        letterSpacing: letterSpacing ?? 0,
        color: textcolor ?? AppColors.black,
        fontSize: fontsize,
        fontStyle: fontStyle,
        fontWeight: fontweight ?? FontWeight.bold,
        decoration: textDecoration,
      )),
      textAlign: textAlign,
      overflow: overflow ?? TextOverflow.ellipsis,
    );
  }
}
