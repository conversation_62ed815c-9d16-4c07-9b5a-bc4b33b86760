import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_widget.dart';


import '../utility/app_assets.dart';
import '../utility/app_colors.dart';

class AppbarTitleWidget extends StatelessWidget {
  const AppbarTitleWidget({super.key, required this.title, required this.subTitle});
  final String title;
  final String subTitle;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.arrow_back,
                  color: AppColors.black,
                ),
              ),
              Container(
                height: 45,
                width: 45,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(
                    image: AssetImage(AppAssets.placeHolderImage),
                    scale: 2,
                  ),
                ),
              ),
            ],
          ),
          30.verticalSpace,
          TextWidget(
            text: title,
            // 'Add Service',
            fontsize: 30,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
          8.verticalSpace,
          TextWidget(
            text: subTitle,

            // 'Fill all the informations required.'

            fontsize: 16,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            textcolor: AppColors.grey4,
            fontweight: FontWeight.w400,
          ),
        ],
      ),
    );
  }
}
