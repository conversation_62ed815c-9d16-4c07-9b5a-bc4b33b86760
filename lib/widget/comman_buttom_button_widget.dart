import 'package:flutter/material.dart';

import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/utility.dart';

class CommonBottomButtonWidget extends StatelessWidget {
  const CommonBottomButtonWidget(
      {super.key,
      required this.cureentIndex,
      required this.selectedIndex,
      required this.image,
      required this.onTap,
      required this.title});
  final int cureentIndex;
  final int selectedIndex;
  final String image;
  final Function() onTap;
  final String title;
  @override
  Widget build(BuildContext context) {
    final isSelected = cureentIndex == selectedIndex;
    return InkWell(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (cureentIndex == 4)
            SizedBox(
              height: 24,
              width: 24,
              child: Utility.imageLoader(
                url: image,
                placeholder: AppAssets.placeHolderImage,
              ),
            )
          else
            Image.asset(
              image,
              height: 20,
              color: isSelected ? AppColors.primaryColor : AppColors.black,
            ),
          const SizedBox(height: 7),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isSelected ? AppColors.primaryColor : AppColors.black,
                ),
          ),
        ],
      ),
    );
  }
}
