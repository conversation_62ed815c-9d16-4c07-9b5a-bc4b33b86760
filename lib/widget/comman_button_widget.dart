import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';

import '../utility/app_colors.dart';

class CommonButtonWidget extends StatelessWidget {
  const CommonButtonWidget(
      {super.key,
      this.onTap,
      required this.text,
      this.buttonColor,
      this.textColor,
      this.boxShadow,
      this.border,
      this.fontWeight,
      this.fontSize,
      this.padding,
      this.image,
      this.maxwidth,
      this.innerPaddding,
      this.borderRadius});
  final void Function()? onTap;
  final String text;
  final Color? buttonColor;
  final Color? textColor;
  final List<BoxShadow>? boxShadow;
  final Border? border;
  final FontWeight? fontWeight;
  final double? fontSize;
  final EdgeInsets? padding;
  final String? image;
  final double? maxwidth;
  final EdgeInsets? innerPaddding;
  final BorderRadiusGeometry? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(0.0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          // width: 300,
          constraints: (maxwidth != null)
              ? BoxConstraints(maxWidth: maxwidth!)
              : getValueForScreenType(context: context, mobile: null, tablet: const BoxConstraints(maxWidth: 450)),
          padding: innerPaddding ?? const EdgeInsets.symmetric(vertical: 22),
          decoration: BoxDecoration(
            border: border,
            color: buttonColor ?? AppColors.primaryColor,
            borderRadius: borderRadius ??
                const BorderRadius.all(
                  Radius.circular(20),
                ),
            boxShadow: boxShadow,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (image != null) ...[
                SizedBox(
                  height: 20,
                  width: 20,
                  child: image != null ? Image.asset(image!) : const SizedBox(),
                ),
                const SizedBox(
                  width: 20,
                ),
              ],
              Flexible(
                child: Text(
                  text,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  style: TextStyle(
                      fontSize: fontSize ?? 15,
                      fontWeight: fontWeight ?? FontWeight.w500,
                      color: textColor ?? AppColors.whiteColor,
                      overflow: TextOverflow.ellipsis),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
