import 'package:flutter/material.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/app_colors.dart';
import 'package:trendi_people/utility/utility.dart';

class ImageView extends StatefulWidget {
  const ImageView({Key? key, this.imageUrl}) : super(key: key);
  final imageUrl;

  @override
  State<ImageView> createState() => _ImageViewState();
}

class _ImageViewState extends State<ImageView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteColor,
      appBar: AppBar(
        centerTitle: true,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
          color: AppColors.black,
        ),
        backgroundColor: AppColors.whiteColor,
      ),
      body: InteractiveViewer(
        panEnabled: true,
        boundaryMargin: const EdgeInsets.all(0),
        minScale: 0.5,
        maxScale: 4,
        child: Utility.imageLoader(
          borderRadius: const BorderRadius.only(topLeft: Radius.circular(8), bottomLeft: Radius.circular(8)),
          url: widget.imageUrl ?? '',
          placeholder: AppAssets.logo,
          fit: BoxFit.fitWidth,
          context: null,
        ),
      ),
    );
  }
}
