// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_widget.dart';

import '../utility/app_colors.dart';

class GenderContainerWidget extends StatelessWidget {
  const GenderContainerWidget(
      {super.key,
      this.image,
      this.name,
      this.onTap,
      this.gender = false,
      this.portfolio = false,
      this.background,
      this.borderColor,
      this.padding,
      this.margin,
      this.icon});
  final String? image;
  final String? name;
  final void Function()? onTap;
  final bool gender;
  final bool portfolio;
  final Color? background;
  final Color? borderColor;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final IconData? icon;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: margin ?? const EdgeInsets.only(top: 20),
        padding: padding ?? const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        decoration: BoxDecoration(
            color: background ?? AppColors.whiteColor,
            border: Border.all(color: borderColor ?? Colors.grey.shade300),
            borderRadius: BorderRadius.circular(20)),
        child: (portfolio)
            ? Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextWidget(
                    text: name ?? '',
                    textcolor: AppColors.black,
                    // fontweight: FontWeight.w600,
                    fontsize: 16,
                  ),
                  Icon(
                    icon ?? Icons.arrow_forward_ios_outlined,
                    color: AppColors.black,
                    size: 20,
                  )
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (gender)
                    Container(
                        padding: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.grey3),
                          shape: BoxShape.circle,
                        ),
                        child: Image.asset(
                          image ?? '',
                          height: 20,
                          width: 20,
                          color: AppColors.black,
                        )),
                  15.horizontalSpace,
                  TextWidget(
                    text: name ?? '',
                    textcolor: AppColors.black,
                    // fontweight: FontWeight.w600,
                    fontsize: 16,
                  )
                ],
              ),
      ),
    );
  }
}
