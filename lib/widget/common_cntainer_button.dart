// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:trendi_people/widget/text_widget.dart';

import '../utility/app_colors.dart';

class ContainerButton extends StatelessWidget {
  const ContainerButton({super.key, this.image, this.name, this.onTap});
  final String? image;
  final String? name;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 40),
        decoration: BoxDecoration(border: Border.all(color: Colors.white24), borderRadius: BorderRadius.circular(20)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              flex: 1,
              child: Image.asset(
                '$image',
                height: 25,
                width: 25,
              ),
            ),
            // 15.horizontalSpace,
            Expanded(
              flex: 3,
              child: TextWidget(
                text: name ?? '',
                textcolor: AppColors.whiteColor,
                fontweight: FontWeight.w500,
                fontsize: 14,
              ),
            )
          ],
        ),
      ),
    );
  }
}
