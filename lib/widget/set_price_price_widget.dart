// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_widget.dart';


class SetPriceWidget extends StatelessWidget {
  const SetPriceWidget({super.key, this.price, this.description});
    final String? price;
  final String? description;


  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration:
          BoxDecoration(border: Border.all(color: Colors.grey.shade300), borderRadius: BorderRadius.circular(20)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            text: price ?? "",
          ),
          5.verticalSpace,
          TextWidget(
            text: description ?? "",
          )
        ],
      ),
    );
  }
}
