// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_widget.dart';

import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/utility.dart';

class ServiceCategoriesWidget extends StatelessWidget {
  const ServiceCategoriesWidget({super.key, this.title, this.image, this.onTap, this.background, this.borderColor});
  final String? title;
  final String? image;
  final void Function()? onTap;
  final Color? background;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size.width * 0.45,
        decoration: BoxDecoration(
          border: Border.all(color: borderColor ?? AppColors.black, width: 1.5),
          borderRadius: BorderRadius.circular(16.0),
          color: background ?? AppColors.whiteColor,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 100,
              width: 150,
              padding: const EdgeInsets.all(10),
              margin: const EdgeInsets.symmetric(horizontal: 35),
              decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.grey4,
                  ),
                  borderRadius: BorderRadius.circular(10),
                  shape: BoxShape.rectangle),
              child: Utility.imageLoader(
                url: image ?? '',
                placeholder: AppAssets.placeHolderImage,
              ),
            ),
            10.verticalSpace,
            TextWidget(
              text: title ?? '',
              textAlign: TextAlign.center,
              fontweight: FontWeight.w600,
              fontsize: 15,
              maxLines: 3,
            )
          ],
        ),
      ),
    );
  }
}
