import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:trendi_people/utility/app_assets.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_widget.dart';

import '../utility/app_colors.dart';
import '../utility/utility.dart';

class FreliancerListWidget extends StatelessWidget {
  const FreliancerListWidget({
    super.key,
    this.margin,
    required this.image,
    required this.name,
    this.category,
    this.isForStar = false,
    required this.rating,
    this.border,
    this.padding,
    this.height,
    required this.onTap,
    required this.onChatTap,
  });
  final String image;
  final String name;
  final String? category;

  final String rating;
  final BoxBorder? border;
  final bool isForStar;
  final EdgeInsetsGeometry? padding;
  final double? height;
  final EdgeInsetsGeometry? margin;
  final VoidCallback onTap;
  final VoidCallback onChatTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      //
      child: Container(
          // margin: const EdgeInsets.symmetric(horizontal: 10),
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          // width: MediaQuery.of(context).size.width,
          // height: MediaQuery.of(context).size.height * 0.1,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: border ?? Border.all(color: AppColors.grey4),
              color: AppColors.white24),
          child: Row(
            children: [
              Container(
                margin: margin ?? const EdgeInsets.only(right: 10),
                height: height ?? 60,
                width: height ?? 60,
                // decoration: const BoxDecoration(
                //   shape: BoxShape.circle,
                // ),
                child: Utility.imageLoader(
                  url: image,
                  isShapeCircular: true,
                  shape: BoxShape.circle,
                  placeholder: AppAssets.placeHolderImage,
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                        text: name, fontsize: 20, textcolor: AppColors.primaryColor, fontweight: FontWeight.bold),
                    7.verticalSpace,
                    if (category != null)
                      TextWidget(
                          text: category ?? "",
                          fontsize: 15,
                          textcolor: AppColors.blackColor,
                          fontweight: FontWeight.w400),
                    Row(
                      children: [
                        IgnorePointer(
                          child: RatingBar(
                            initialRating: double.tryParse(rating) ?? 0.0,
                            direction: Axis.horizontal,
                            itemSize: 20,
                            allowHalfRating: true,
                            itemCount: 5,
                            ratingWidget: RatingWidget(
                              full: Container(
                                  height: 20,
                                  width: 20,
                                  padding: const EdgeInsets.all(5),
                                  margin: const EdgeInsets.all(2),
                                  decoration:
                                      const BoxDecoration(color: AppColors.primaryColor, shape: BoxShape.circle),
                                  child: Image.asset(
                                    // fit: BoxFit.cover,
                                    AppAssets.ratingWhite,
                                  )),
                              half: Container(
                                  height: 75,
                                  width: 75,
                                  padding: const EdgeInsets.all(5),
                                  margin: const EdgeInsets.all(2),
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                  ),
                                  child: Image.asset(
                                    AppAssets.halfIcon,
                                    fit: BoxFit.contain,
                                  )),
                              empty: Container(
                                  height: 20,
                                  width: 20,
                                  padding: const EdgeInsets.all(5),
                                  margin: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(color: AppColors.grey3, shape: BoxShape.circle),
                                  child: Image.asset(
                                    AppAssets.ratingBlack,
                                    height: 10,
                                  )),
                            ),
                            itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                            onRatingUpdate: (rating) {
                              print(rating);
                            },
                          ),
                        ),
                        TextWidget(
                          text: rating,
                          fontsize: 12,
                          textcolor: AppColors.grey4,
                        ),
                        !isForStar
                            ? Flexible(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    IconButton(
                                      onPressed: onChatTap,
                                      icon: const Icon(Icons.chat_outlined),
                                    ),
                                  ],
                                ),
                              )
                            : Container()
                      ],
                    )
                  ],
                ),
              ),
            ],
          )),
    );
  }
}
