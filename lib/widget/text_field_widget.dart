import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:responsive_builder/responsive_builder.dart';

import '../utility/app_colors.dart';

class AppTextField extends StatelessWidget {
  const AppTextField(
      {Key? key,
      this.suffix,
      this.suffixIcon,
      this.preffixIcon,
      this.customBorder,
      this.labelText,
      this.hintText,
      required this.controller,
      this.textAlign,
      this.prefixText,
      this.textInputAction,
      this.focusBorder,
      this.inputFormatters,
      this.enableBorder,
      this.maxLines,
      this.hintDecoration,
      this.suffixText,
      this.hintStyle,
      this.keyboardType,
      this.labelStyle,
      this.focusNode,
      this.enabled,
      this.label,
      this.padding,
      this.onChanged,
      this.onTap,
      this.validator,
      this.obscureText,
      this.inputTextStyle,
      this.textColor,
      this.filled,
      this.fillColor,
      this.contentPadding,
      this.minLines,
      this.readOnly = false,
      this.counterText,
      this.maxlength,
      this.counterStyle,
      this.hightTextField,
      this.borderColor,
      this.borderRadius})
      : super(key: key);

  final Widget? suffix;
  final Widget? suffixIcon;
  final Widget? preffixIcon;
  final InputBorder? customBorder;
  final String? labelText;
  final String? hintText;
  final TextEditingController controller;
  final TextAlign? textAlign;
  final String? prefixText;
  final TextInputAction? textInputAction;
  final List<TextInputFormatter>? inputFormatters;
  final InputBorder? focusBorder;
  final InputBorder? enableBorder;
  final int? maxLines;
  final int? minLines;
  final TextStyle? counterStyle;
  final String? counterText;
  final TextDecoration? hintDecoration;
  final String? suffixText;
  final TextStyle? hintStyle;
  final TextInputType? keyboardType;
  final TextStyle? labelStyle;
  final FocusNode? focusNode;
  final bool? enabled;
  final String? label;
  final EdgeInsets? padding;
  final Function(String)? onChanged;
  final Function()? onTap;
  final String? Function(String?)? validator;
  final bool? obscureText;
  final TextStyle? inputTextStyle;
  final Color? textColor;
  final bool? filled;
  final Color? fillColor;
  final EdgeInsetsGeometry? contentPadding;
  final BorderRadius? borderRadius;
  final bool readOnly;
  final double? hightTextField;
  final int? maxlength;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        constraints: getValueForScreenType(context: context, mobile: null, tablet: const BoxConstraints(maxWidth: 450)),
        height: hightTextField,
        decoration: BoxDecoration(color: AppColors.transparent, borderRadius: BorderRadius.circular(20)),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(0),
          child: TextFormField(
              maxLength: maxlength,
              inputFormatters: inputFormatters,
              readOnly: readOnly,
              validator: validator,
              onChanged: onChanged,
              style: inputTextStyle,
              obscureText: obscureText ?? false,
              enabled: (onTap != null) ? false : enabled,
              keyboardType: keyboardType,
              textInputAction: textInputAction,
              textAlign: textAlign ?? TextAlign.start,
              controller: controller,
              maxLines: maxLines ?? 1,
              minLines: minLines,
              focusNode: focusNode,
              decoration: InputDecoration(
                counterText: counterText,
                counterStyle: counterStyle,
                contentPadding: contentPadding,
                prefixIcon: preffixIcon,
                suffixIcon: suffixIcon,
                filled: filled,
                fillColor: fillColor ?? AppColors.whiteColor,
                label: label != null ? Text(label!) : null,
                labelText: labelText,
                labelStyle: labelStyle ?? const TextStyle(color: Colors.white, fontSize: 50),
                focusedErrorBorder: OutlineInputBorder(
                    borderRadius: borderRadius ?? BorderRadius.circular(20),
                    borderSide: BorderSide(
                      color: borderColor ?? Colors.white24,
                    )),
                disabledBorder: OutlineInputBorder(
                    borderRadius: borderRadius ?? BorderRadius.circular(20),
                    borderSide: BorderSide(
                      color: borderColor ?? Colors.white24,
                    )),
                errorBorder: OutlineInputBorder(
                    borderRadius: borderRadius ?? BorderRadius.circular(20),
                    borderSide: BorderSide(
                      color: borderColor ?? Colors.white24,
                    )),
                focusedBorder: OutlineInputBorder(
                    borderRadius: borderRadius ?? BorderRadius.circular(20),
                    borderSide: BorderSide(
                      color: borderColor ?? Colors.white24,
                    )),
                enabledBorder: OutlineInputBorder(
                    borderRadius: borderRadius ?? BorderRadius.circular(20),
                    borderSide: BorderSide(
                      color: borderColor ?? Colors.white24,
                    )),
                prefixText: prefixText,
                border: customBorder,
                suffix: suffix,
                hintText: hintText,
                suffixText: suffixText,
                hintStyle: hintStyle ??
                    const TextStyle(color: AppColors.whiteColor, fontWeight: FontWeight.w400, fontSize: 15),
              )),
        ),
      ),
    );
  }
}
