// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:trendi_people/utility/sizedbox_extension.dart';
import 'package:trendi_people/widget/text_widget.dart';

import '../utility/app_assets.dart';
import '../utility/app_colors.dart';
import '../utility/utility.dart';

class ChatUserWidget extends StatelessWidget {
  const ChatUserWidget({
    Key? key,
    required this.name,
    this.category,
    required this.time,
    this.number,
    required this.onTap,
    this.imageUrl,
  }) : super(key: key);
  final String name;
  final String? category;
  final String time;
  final String? number;
  final VoidCallback onTap;
  final String? imageUrl;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.only(right: 10),
            height: 60,
            width: 60,
            decoration: const BoxDecoration(shape: BoxShape.circle),
            clipBehavior: Clip.hardEdge,
            child: Utility.imageLoader(url: imageUrl ?? '', placeholder: AppAssets.placeHolderImage),
          ),
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextWidget(
                          text: name, fontsize: 15, textcolor: AppColors.blackColor, fontweight: FontWeight.bold),
                    ),
                    TextWidget(text: time, fontsize: 13, textcolor: AppColors.blackColor, fontweight: FontWeight.w400),
                  ],
                ),
                7.verticalSpace,
                Row(
                  children: [
                    // const Icon(
                    //   Icons.checklist_rounded,
                    //   color: AppColors.primaryColor,
                    // ),
                    Expanded(
                      child: TextWidget(
                          text: category ?? "",
                          fontsize: 15,
                          textcolor: AppColors.blackColor,
                          fontweight: FontWeight.w400),
                    ),
                    if (number != null)
                      Container(
                        padding: const EdgeInsets.all(5),
                        decoration: const BoxDecoration(shape: BoxShape.circle, color: AppColors.primaryColor),
                        child: TextWidget(
                            text: number!, fontsize: 10, textcolor: AppColors.blackColor, fontweight: FontWeight.w400),
                      ),
                  ],
                ),
                7.verticalSpace,
              ],
            ),
          ),
        ],
      ),
    );
  }
}
