import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:trendi_people/model/chat_user_model.dart';

class FireaseChatService {
  static String chatUserList = "users_chat_list";
  static String chatMessagesCollections = "messages";
  static CollectionReference userschatlistsCollection = FirebaseFirestore.instance.collection(chatUserList);
  static CollectionReference<Map<String, dynamic>> usersMessagesCollection =
      FirebaseFirestore.instance.collection(chatMessagesCollections);

  static Query<ChatUserModel> getMessagesQuery({required String uniqueKey}) {
    return usersMessagesCollection
        .where('group_id', isEqualTo: uniqueKey)
        .orderBy('time_stamp', descending: true)
        .withConverter(
            fromFirestore: (snapshot, options) => ChatUserModel.fromJson(snapshot.data()!),
            toFirestore: (chatUser, options) => chatUser.toJson());
  }

  static generateUniqueKey({required int userId1, required int userId2}) {
    List<int> list = [userId1, userId2];
    list.sort();
    return '${list[0]}-${list[1]}';
  }

  static Future<void> enterDatainUsersChatListCollection({
    String? message,
    // String? chatType,
    required String useraId,
    required String userbId,
    String? groupchatid,
    String? myid,
    String? imageUrl,
  }) async {
    List<String> idsarray = [useraId, userbId];

    QuerySnapshot qs = await userschatlistsCollection.where('group_id', isEqualTo: groupchatid).get();
    if (qs.docs.isNotEmpty) {
      userschatlistsCollection.doc(qs.docs[0].id).update({
        if (imageUrl != null) 'image_url': imageUrl,
        'type': imageUrl != null ? 'image' : 'message',
        "message": message,
        "time_stamp": FieldValue.serverTimestamp(),
        "usera_badge": FieldValue.increment(
          (1),
        ),
        "userb_badge": FieldValue.increment(
          (0),
        ),
      });
    } else {
      userschatlistsCollection.doc().set({
        if (imageUrl != null) 'image_url': imageUrl,
        "usera_id": useraId,
        "userb_id": userbId,
        'type': imageUrl != null ? 'image' : 'message',
        "group_id": groupchatid,
        "id_array": idsarray,
        "message": message,
        "usera_badge": 1,
        "userb_badge": 0,
        "time_stamp": FieldValue.serverTimestamp(),
      });
    }
  }

  static Future<void> enterChatsData({
    required String myId,
    required String otherId,
    required String groupChatId,
    required String content,
    // required String type,
    String? imageUrl,
  }) async {
    await usersMessagesCollection.doc().set({
      if (imageUrl != null) 'image_url': imageUrl,
      'userb_id': myId,
      'usera_id': otherId,
      'group_id': groupChatId,
      'time_stamp': FieldValue.serverTimestamp(),
      'message': content,
      'sender_id': myId,
      'type': imageUrl != null ? 'image' : 'message',
    });
  }

  static Future<void> updatePaymentMessageStatus({
    required String messageId,
    required String status,
  }) async {
    await usersMessagesCollection.doc(messageId).update({
      'payment_status': status,
    });
  }

  static Future<void> messagesreadupdatebadgevaluetozero(String groupChatId) async {
    QuerySnapshot qs = await userschatlistsCollection.where('group_id', isEqualTo: groupChatId).get();
    if (qs.docs.isNotEmpty) {
      userschatlistsCollection.doc(qs.docs[0].id).update({"userb_badge": 0});
    }
  }

  static Stream<QuerySnapshot> getUsersChatListIndividualBadgeValue(int myId, int otherId) {
    return userschatlistsCollection
        .where('group_id', isEqualTo: generateUniqueKey(userId1: myId, userId2: otherId))
        .snapshots();
  }

  static Future<String?> uploadFile({File? file, required String filepath}) async {
    if (file == null) {
      return null;
    }
    final ref = FirebaseStorage.instance.ref("images").child(filepath);
    final uploadTask = await ref.putFile(File(file.path));
    if (uploadTask.state == TaskState.success) {
      final downloadUrl = await ref.getDownloadURL();
      return downloadUrl;
    } else {
      uploadTask.state;
    }
    return null;
  }
}
