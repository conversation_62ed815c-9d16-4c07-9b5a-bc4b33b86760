class AppString {
  static const String languageKey = 'LANGUAGE_KEY';
  static const String userLocationKey = 'Location Key';

  static const String stripeKey =
      'pk_live_51JImb5ATxjBgFaT5yZ6tI1gU5klx7qMmbOlRa0lCmmby9uKxwj6Qmv4P4ZOAQ1i4oDBDtGitAiTM3Omouswf6YuE00ZlXNvSXe';
  // static const String stripeKey =
  //     'pk_test_51JImb5ATxjBgFaT5qpugjJrCppHSzSTSjrcBErSEn5IRJ5zNvSMGTK6dVmysd4dQyjkAxICzDVluIuhwTzNFGOPU00oQ1kURvv';

  static const String userToken = 'USER_TOKEN';
  static const String userPrefKey = 'USERPREF_KEY';
  static const String storageUrl = 'https://trendi-people-s3.s3.eu-west-2.amazonaws.com/';
  static const tokenKey = "TOKEN";
  static const String baseUrl = "https://web.trendipeople.com/api/";
  static const String loginUrl = '${baseUrl}v1/users/login-app';
  static const String userDetail = '${baseUrl}v1/users/detail';
  static const String sendNotification = '${baseUrl}v1/chat/send-notification';
  static const String resetpasswordUrl = '${baseUrl}v1/users/forgot-password';
  static const String signupUrl = '${baseUrl}v1/users/create';
  static const String categoryListUrl = '${baseUrl}v1/categories';
  static const String freeliancerListUrl = '${baseUrl}v1/freelancers';
  static const String submitOrderRequestUrl = '${baseUrl}v1/orders/create-checkout';
  static const String editUserUrl = '${baseUrl}v1/users/edit';
  static const String orderHistoryUrl = '${baseUrl}v1/orders/';
  static const String changePasswordUrl = '${baseUrl}v1/users/change-password';
  static const String logoutUrl = '${baseUrl}v1/users/logout-app';
  static String freeliancerDetailUrl(int id) => "${baseUrl}v1/freelancers/$id";
  static String pricingUrl(int id) => "${baseUrl}v1/freelancers/$id/pricing";
  static String getOrderDetailUrl(int id) => "${baseUrl}v1/orders/$id";
  static const String mapBaseUrl = "https://maps.googleapis.com/maps/api/staticmap";
  static const String mapAPIKEY = 'AIzaSyBOOXEMMpDtojNMDmRe3rGjr-s77LC15PY';
  static const String notificationList = '${baseUrl}v1/notifications/';
  static const String chatList = '${baseUrl}v1/chat/list';
  static const String feedbackUrl = '${baseUrl}v1/reviews/add-review';
  static const String reviewListUrl = '${baseUrl}v1/reviews/';
  static const String reportUrl = '${baseUrl}v1/reports';
  static const String reportFreelancerUrl = baseUrl;
  static String delereUserUrl(int id) => "${baseUrl}v1/users/$id/delete";

  static String payForChatOrder(String id) => "${baseUrl}v1/orders/$id/pay-for-chat-order";

  static const String chatMessageTypeMessage = 'message';
  static const String chatMessageTypeImage = 'image';
  static const String chatMessageTypePayment = 'payment';

  static const String chatPaymentStatusPending = 'pending';
  static const String chatMessageTypeFail = 'fail';
  static const String chatMessageTypeSuccess = 'success';
}
