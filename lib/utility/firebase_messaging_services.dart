// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:trendi_people/screen/chat/chat_detail_screen.dart';

import '../responce/notification_app_responce.dart';

Future<void> _onBackgroundMessageHandler(RemoteMessage message) async {
  log('on background message handler $message');
  log(message.data['data'].toString());
}

class FirebaseMessagingService {
  factory FirebaseMessagingService() {
    return _singleton;  
  }

  FirebaseMessagingService._internal();

  static final FirebaseMessagingService _singleton = FirebaseMessagingService._internal();

  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static String? token;

  late AndroidNotificationChannel channel;

  late StreamSubscription<RemoteMessage> onFrontEndStream;
  late StreamSubscription<RemoteMessage> onOpenAppStream;
  late StreamSubscription<String> tokenStream;

  static Future<void> initializeMain() async {
    await Firebase.initializeApp();
    token = await _firebaseMessaging.getToken() ?? '';
    log('TOKENNNNNNNNNNNNN $token');
    FirebaseMessaging.onBackgroundMessage(_onBackgroundMessageHandler);
  }

  Future<void> initialize({Function(RemoteMessage remoteMessage)? onMessage, required BuildContext context}) async {
    await _firebaseMessaging.requestPermission(
      announcement: true,
      carPlay: true,
      criticalAlert: true,
    );

    if (onMessage != null) {
      await _onOpenedAppFromTerminateMessage(onMessage);
    }

    if (!kIsWeb) {
      channel = const AndroidNotificationChannel(
        'high_importance_channel', // id
        'High Importance Notifications', // title
        description: 'This channel is used for important notifications.', // description
        importance: Importance.high,
      );

      const initializationSettingsAndroid = AndroidInitializationSettings('@drawable/ic_notifications');

      final initializationSettingsIOS = DarwinInitializationSettings(
        onDidReceiveLocalNotification: (_, __, ___, ____) async {
          log('IOS NOTIFICATION');
        },
      );

      final initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (value) => _onOpenedLocalNotification(value, context),
      );

      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);

      await _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      await _onFrontEndMessage(flutterLocalNotificationsPlugin);
    }

    // await _setToken();

    _onOpenedAppMessage(context);
  }

  Future<void> _onOpenedAppFromTerminateMessage(Function(RemoteMessage initialMessage) onMessage) async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      onMessage(initialMessage);
    }
    log('on teminated opended App message $initialMessage');
  }

  Future<void> _onOpenedLocalNotification(NotificationResponse? response, BuildContext context) async {
    if (response?.payload != null) {
      final payload = response!.payload!;
      log("aaa::: $payload");

      final notification = NotificationAppResponce.fromJson(jsonDecode(jsonDecode(payload)['data']));
      if (notification.clickAction != null) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ChatDetailScreen(
              otherUserId: notification.userData?.id ?? 0,
              otherUserName: notification.userData?.name,
            ),
          ),
        );
      }
    }
  }

  static Future<void> deleteToken() async {
    await FirebaseMessaging.instance.deleteToken();
    log('Firebase Token Deleted');
  }

  void _onOpenedAppMessage(BuildContext context) {
    FirebaseMessaging.onMessageOpenedApp.listen((message) async {
      log('body - ${message.data}');
      log('on frontend opended App message $message');
      log('body - ${message.data}');
      log('on frontend opended App message $message');
      final notification = NotificationAppResponce.fromJson(jsonDecode(message.data['data']));
      if (notification.clickAction != null) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ChatDetailScreen(
              otherUserId: notification.userData?.id ?? 0,
              otherUserName: notification.userData?.name,
            ),
          ),
        );
      }
    });
  }

  Future<void> _onFrontEndMessage(FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin) async {
    onFrontEndStream = FirebaseMessaging.onMessage.listen(
      (message) async {
        final notification = message.notification;
        final android = message.notification?.android;
        if (notification != null && android != null && !kIsWeb) {
          await flutterLocalNotificationsPlugin.show(
            notification.hashCode,
            notification.title,
            notification.body,
            NotificationDetails(
              android: AndroidNotificationDetails(
                channel.id,
                channel.name,
                channelDescription: channel.description,
                icon: '@drawable/ic_notifications',
              ),
            ),
            payload: jsonEncode(message.data),
          );
        }
      },
      onError: (dynamic error) {
        log('ON FONTEND MESSAGE ERROR $error');
      },
      onDone: () {
        log('ON FONTEND MESSAGE DONE');
      },
    );
  }

  // Future _setToken() async {
  //   token = await _firebaseMessaging.getToken() ?? '';

  //   tokenStream = _firebaseMessaging.onTokenRefresh.listen((newToken) {
  //     token = newToken;
  //   });
  // }

  void dispose() {
    onFrontEndStream.cancel();
    onOpenAppStream.cancel();
    tokenStream.cancel();
  }
}
