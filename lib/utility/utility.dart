import 'dart:convert';
import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../model/user_model.dart';
import 'app_colors.dart';
import 'app_string.dart';

class Utility {
  static void toast({required String? message, Color? color}) {
    if (message != null) {
      Fluttertoast.showToast(msg: message, backgroundColor: color ?? AppColors.primaryColor);
    }
  }

  static Widget imageLoader(
      {required String url,
      required String placeholder,
      BoxFit? fit,
      BuildContext? context,
      bool isShapeCircular = false,
      BorderRadius? borderRadius,
      BoxShape? shape}) {
    if (url.trim() == '') {
      return Container(
        decoration: BoxDecoration(
          shape: shape ?? BoxShape.rectangle,
          borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(10),
          image: DecorationImage(
            image: AssetImage(placeholder),
            fit: fit ?? BoxFit.cover,
          ),
        ),
      );
    }
    if (!url.startsWith('http')) url = AppString.storageUrl + url;
    return CachedNetworkImage(
      imageUrl: url,
      imageBuilder: (context, imageProvider) => Container(
        decoration: BoxDecoration(
          borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(10),
          // borderRadius: borderRadius ?? BorderRadius.circular(10),
          shape: shape ?? BoxShape.rectangle,
          image: DecorationImage(
            image: imageProvider,
            fit: fit ?? BoxFit.cover,
          ),
        ),
      ),
      errorWidget: (context, error, dynamic a) => Container(
        decoration: BoxDecoration(
          shape: shape ?? BoxShape.rectangle,
          borderRadius: isShapeCircular ? null : borderRadius ?? BorderRadius.circular(10),
          // borderRadius: borderRadius ??  BorderRadius.circular(10),
          image: DecorationImage(
            image: AssetImage(placeholder),
            fit: fit ?? BoxFit.cover,
          ),
        ),
      ),
      placeholder: (context, url) => progress(),
    );
  }

  static String getMapUrl(String latitude, String longitude) {
    if (latitude == '' || longitude == '') {
      return 'Error';
    }
    String width = "512";
    String height = "512";
    log("${AppString.mapBaseUrl}?zoom=19&size=${width}x$height&maptype=roadmap&markers=$latitude,$longitude&key=${AppString.mapAPIKEY}");

    return "${AppString.mapBaseUrl}?zoom=17&size=${width}x$height&maptype=roadmap&markers=$latitude,$longitude&key=${AppString.mapAPIKEY}";
  }

  static bool isValidEmail(String email) {
    return RegExp(
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
        .hasMatch(email);
  }

  static Widget progress({Color? color}) {
    return Center(
      child: CircularProgressIndicator(
        color: color ?? AppColors.primaryColor,
      ),
    );
  }

  static setToken({required String key, required String value}) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setString(key, value);
  }

  static Future<String?> getToken({required String key}) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    final token = pref.getString(
      key,
    );
    return token;
  }

  static setPref({required String key, required String value}) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setString(key, value);
  }

  static Future<String?> getPref({required String key}) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    final token = pref.getString(
      key,
    );
    return token;
  }

  static setUser(UserModel user) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    final users = jsonEncode(user);
    pref.setString(AppString.userPrefKey, users);
  }

  static Future<UserModel?> getUser() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    final user = pref.getString(AppString.userPrefKey);
    if (user != null) {
      final userModel = UserModel.fromJson(jsonDecode(user));
      return userModel;
    }
    return null;
  }

  static void clearPref() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.clear();
  }

  static Widget noDataWidget({required String text}) {
    return Center(
      child: Text(
        text,
        style: const TextStyle(
          color: AppColors.primaryColor,
          fontSize: 18,
        ),
      ),
    );
  }

  static String timeStamp(String time) {
    int timestamp = (int.tryParse(time) ?? 0);
    DateTime day = DateTime.fromMillisecondsSinceEpoch(timestamp);
    DateTime currentDay = DateTime.now();
    if (day.day == currentDay.day && day.month == currentDay.month && day.year == currentDay.year) {
      String date = DateFormat.jm().format(day.toLocal());
      return date;
    } else {
      String date = DateFormat.jm().add_yMd().format(day.toLocal());
      return date;
    }
  }
}
