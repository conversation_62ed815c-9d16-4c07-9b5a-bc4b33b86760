import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app_colors.dart';

ThemeData themeData = ThemeData(
  appBarTheme: AppBarTheme(
    //   color: AppColors.,
    elevation: 0,
    centerTitle: true,
    systemOverlayStyle: SystemUiOverlayStyle.light,
    titleTextStyle: GoogleFonts.roboto(
      fontSize: 16,
      fontWeight: FontWeight.w600,
    ),
  ),
  bottomSheetTheme: const BottomSheetThemeData(
    //  backgroundColor: AppColors.purpleDarkestColor,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(16),
        topRight: Radius.circular(16),
      ),
    ),
  ),

  scaffoldBackgroundColor: AppColors.white24,
  textTheme: TextTheme(
    displayLarge: GoogleFonts.roboto(
      fontSize: 99,
      fontWeight: FontWeight.w300,
      letterSpacing: -1.5,
    ),
    displayMedium: GoogleFonts.roboto(
      fontSize: 62,
      fontWeight: FontWeight.w300,
      letterSpacing: -0.5,
    ),
    displaySmall: GoogleFonts.roboto(fontSize: 49, fontWeight: FontWeight.w400),
    headlineMedium: GoogleFonts.roboto(
      fontSize: 35,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
    ),
    headlineSmall: GoogleFonts.roboto(fontSize: 25, fontWeight: FontWeight.w400),
    titleLarge: GoogleFonts.roboto(
      fontSize: 21,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.15,
    ),
    titleMedium: GoogleFonts.roboto(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.15,
    ),
    titleSmall: GoogleFonts.roboto(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
    ),
    bodyLarge: GoogleFonts.roboto(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
    ),
    bodyMedium: GoogleFonts.roboto(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
    ),
    labelLarge: GoogleFonts.roboto(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 1.25,
    ),
    bodySmall: GoogleFonts.roboto(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
    ),
    labelSmall: GoogleFonts.roboto(
      fontSize: 10,
      fontWeight: FontWeight.w400,
      letterSpacing: 1.5,
    ),
  ),
  highlightColor: Colors.transparent,
  // splashColor: AppColors.purpleDarkestColor.withOpacity(0.3),
  // backgroundColor: AppColors.purpleDarkestColor,
  pageTransitionsTheme: const PageTransitionsTheme(
    builders: {
      TargetPlatform.android: CupertinoPageTransitionsBuilder(),
      TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
    },
  ),
  tabBarTheme: TabBarTheme(
    labelStyle: GoogleFonts.roboto(fontWeight: FontWeight.bold),
    unselectedLabelStyle: GoogleFonts.roboto(),
  ),
  // colorScheme: ColorScheme.fromSwatch(
  //   accentColor: Colors.,
  //   // backgroundColor: AppColors.purpleDarkestColor,
  //   brightness: Brightness.dark,
  // ),
);
